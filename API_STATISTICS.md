# 宠物竞争对手系统统计分析API

## 接口规范

与主API文档中的接口规范一致。

## 1. 门店统计数据接口

### 1.1 获取门店统计数据

获取门店的总体统计数据，包括总门店数量、本月新增门店数量、最近7天更新的门店数量，以及各种分布统计信息。

#### 请求

- 方法: `GET`
- 路径: `/api/stores/statistics`
- 参数:
  - `business_district`: 商圈/区域名称（可选）
  - `store_type`: 门店类型（可选）
  - `competition_relation`: 竞争关系（可选）
  - `start_date`: 开始日期，格式：YYYY-MM-DD（可选）
  - `end_date`: 结束日期，格式：YYYY-MM-DD（可选）

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "total_stores": 86,
    "new_stores_this_month": 5,
    "updated_stores_last_week": 12,
    "store_type_distribution": [
      {
        "store_type": "direct_chain",
        "count": 35
      },
      {
        "store_type": "franchise_chain",
        "count": 28
      },
      {
        "store_type": "single_store",
        "count": 23
      }
    ],
    "competition_relation_distribution": [
      {
        "competition_relation": "direct_competitor",
        "count": 56
      },
      {
        "competition_relation": "indirect_hospital",
        "count": 17
      },
      {
        "competition_relation": "indirect_retail",
        "count": 13
      }
    ],
    "business_district_distribution": [
      {
        "business_district": "朝阳CBD",
        "count": 15
      },
      {
        "business_district": "海淀中关村",
        "count": 12
      }
    ],
    "rating_distribution": [
      {
        "rating_level": "A+",
        "count": 8
      },
      {
        "rating_level": "A",
        "count": 18
      },
      {
        "rating_level": "B+",
        "count": 22
      }
    ],
    "monthly_new_stores": [
      {
        "year": 2023,
        "month": 5,
        "count": 5
      },
      {
        "year": 2023,
        "month": 4,
        "count": 6
      }
    ],
    "filters_applied": {
      "business_district": "朝阳CBD",
      "store_type": null,
      "competition_relation": null,
      "start_date": null,
      "end_date": null
    },
    "date_info": {
      "today": "2023-05-21",
      "first_day_of_month": "2023-05-01",
      "seven_days_ago": "2023-05-14"
    }
  }
}
```

### 1.2 门店数据导出

导出门店数据为CSV或Excel格式，支持各种过滤条件。

#### 请求

- 方法: `GET`
- 路径: `/api/stores/export`
- 参数:
  - `format`: 导出格式，支持 "csv"、"excel"（默认为 "excel"）
  - `business_district`: 商圈/区域名称（可选）
  - `store_type`: 门店类型（可选）
  - `competition_relation`: 竞争关系（可选）
  - `start_date`: 开始日期，格式：YYYY-MM-DD（可选）
  - `end_date`: 结束日期，格式：YYYY-MM-DD（可选）

#### 响应

直接返回文件流，用于下载文件。

#### 响应头

```
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
Content-Disposition: attachment; filename="stores_export_20230521.xlsx"
```

## 2. 活动统计接口

### 2.1 获取活动统计数据

获取活动的统计数据，包括各种状态的活动数量、按类型的分布、按影响级别的分布等信息。

#### 请求

- 方法: `GET`
- 路径: `/api/activities/statistics`
- 参数:
  - `store`: 门店ID（可选）
  - `activity_type`: 活动类型（可选）
  - `impact_level`: 影响级别（可选）
  - `start_date`: 开始日期，格式：YYYY-MM-DD（可选）
  - `end_date`: 结束日期，格式：YYYY-MM-DD（可选）

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "activity_status": {
      "active": 15,
      "upcoming": 8,
      "ended": 42,
      "recurring": 5,
      "total": 70
    },
    "activity_type_distribution": [
      {
        "activity_type": "promotion",
        "count": 35
      },
      {
        "activity_type": "event",
        "count": 18
      },
      {
        "activity_type": "new_product",
        "count": 10
      },
      {
        "activity_type": "price_change",
        "count": 7
      }
    ],
    "impact_level_distribution": [
      {
        "impact_level": "high",
        "count": 12
      },
      {
        "impact_level": "medium",
        "count": 28
      },
      {
        "impact_level": "low",
        "count": 30
      }
    ],
    "store_activity_distribution": [
      {
        "store__name": "宠物乐园旗舰店",
        "count": 8
      },
      {
        "store__name": "萌宠天地",
        "count": 6
      }
    ],
    "monthly_activity_distribution": [
      {
        "year": 2023,
        "month": 5,
        "count": 12
      },
      {
        "year": 2023,
        "month": 4,
        "count": 15
      }
    ],
    "filters_applied": {
      "store_id": "101",
      "activity_type": null,
      "impact_level": "high",
      "start_date": "2023-01-01",
      "end_date": null
    },
    "date_info": {
      "today": "2023-05-21"
    }
  }
}
```

## 3. 数据趋势分析接口

### 3.1 获取数据趋势

获取指定指标的历史趋势数据，支持按天、周、月进行聚合分析。

#### 请求

- 方法: `GET`
- 路径: `/api/analytics/trends`
- 参数:
  - `metric`: 指标名称，支持 "new_stores"、"activities"、"ratings"（必填）
  - `period`: 周期，支持 "day"、"week"、"month"（默认为 "month"）
  - `start_date`: 开始日期，格式：YYYY-MM-DD（可选，默认为6个月前）
  - `end_date`: 结束日期，格式：YYYY-MM-DD（可选，默认为今天）
  - `store_type`: 门店类型（可选）
  - `business_district`: 商圈/区域名称（可选）

#### 响应

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "metric": "new_stores",
    "period": "month",
    "trends": [
      {
        "period_label": "2023-01",
        "value": 8
      },
      {
        "period_label": "2023-02",
        "value": 5
      },
      {
        "period_label": "2023-03",
        "value": 7
      },
      {
        "period_label": "2023-04",
        "value": 6
      },
      {
        "period_label": "2023-05",
        "value": 5
      }
    ],
    "filters_applied": {
      "start_date": "2023-01-01",
      "end_date": "2023-05-21",
      "store_type": null,
      "business_district": null
    },
    "summary": {
      "total": 31,
      "avg_per_period": 6.2,
      "max_period": "2023-01",
      "max_value": 8,
      "min_period": "2023-02",
      "min_value": 5
    }
  }
}
```

## 4. 指标说明

### 4.1 门店指标

- `total_stores`: 总门店数量
- `new_stores_this_month`: 本月新增门店数量
- `updated_stores_last_week`: 最近7天更新的门店数量
- `store_type_distribution`: 按门店类型的分布
- `competition_relation_distribution`: 按竞争关系的分布
- `business_district_distribution`: 按商圈区域的分布
- `rating_distribution`: 按评级的分布
- `monthly_new_stores`: 按月的新增门店分布

### 4.2 活动指标

- `activity_status`: 各状态的活动数量
- `activity_type_distribution`: 按活动类型的分布
- `impact_level_distribution`: 按影响级别的分布
- `store_activity_distribution`: 按门店的活动分布
- `monthly_activity_distribution`: 按月的活动分布

### 4.3 趋势分析指标

- `new_stores`: 新增门店趋势
- `activities`: 活动数量趋势
- `ratings`: 评级得分趋势

## 5. 数据过滤说明

所有统计接口都支持以下过滤条件：

- 日期范围过滤: 通过`start_date`和`end_date`参数指定
- 门店类型过滤: 通过`store_type`参数指定
- 商圈区域过滤: 通过`business_district`参数指定
- 竞争关系过滤: 通过`competition_relation`参数指定

各接口的特定过滤条件在上述API说明中列出。

## 6. 错误处理

所有统计接口在遇到错误时会返回相应的错误信息，常见错误包括：

- 参数格式错误
- 无效的时间范围
- 缺少必要参数
- 服务器内部错误

错误响应示例：

```json
{
  "code": 400,
  "message": "error",
  "data": {
    "error": "日期格式错误，请使用YYYY-MM-DD格式"
  }
}
``` 