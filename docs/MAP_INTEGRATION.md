# 高德地图集成文档

## 概述
本文档详细说明了宠物零售竞品分析系统中高德地图API的集成方案、组件设计及使用方法。系统使用高德地图提供地理位置可视化、地址搜索和地理编码等功能，增强了门店位置信息的管理和展示能力。

## 配置信息

### API密钥

系统使用两种不同的密钥：
1. **Web端JavaScript API密钥**: `da2ed03e1fd1b75105f23957ba77c803`
   - 用于Web页面中加载地图、创建地图实例和使用各种地图服务
   
2. **安全密钥**: `953ce94c1e646e2898a74576983fd9d7`
   - 用于确保API调用的安全性，防止API密钥被盗用

### 主要配置项

```javascript
// 基本配置
const AMAP_KEY = 'da2ed03e1fd1b75105f23957ba77c803';
const AMAP_SECURITY_CODE = '953ce94c1e646e2898a74576983fd9d7';

// AMap加载配置
const AMAP_CONFIG = {
  key: AMAP_KEY,
  version: '2.0',
  plugins: [
    'AMap.Geocoder',
    'AMap.PlaceSearch',
    'AMap.ToolBar',
    'AMap.Scale',
    'AMap.AutoComplete',
    'AMap.Marker'
  ],
  AMapUI: {
    version: '1.1',
    plugins: []
  },
  Loca: {
    version: '2.0'
  }
};

// 安全配置设置
window._AMapSecurityConfig = { securityJsCode: AMAP_SECURITY_CODE };
```

## 核心组件

系统中有两个核心的地图组件：

### 1. AMapLocationPicker 组件

位置选择器组件，用于添加和编辑门店时选择位置。提供以下功能：

- 地图显示和位置选择
- 地址搜索（带自动完成）
- 标记拖拽
- 反向地理编码（由坐标获取详细地址）
- 当前位置定位
- 地图重载功能

**基本用法：**
```jsx
<AMapLocationPicker 
  onLocationSelect={handleLocationSelect} 
  onAddressChange={handleAddressChange}
  initialLocation={{ lng: 116.397428, lat: 39.90923 }} 
  initialAddress="北京市东城区王府井大街1号"
  readOnly={false}
/>
```

### 2. AMapDisplay 组件

位置展示组件，用于门店详情页显示位置。提供以下功能：

- 静态地图显示
- 地址标注
- 信息窗体显示
- 地图控件（缩放、比例尺等）
- 通过地址查找位置（在没有经纬度的情况下）

**基本用法：**
```jsx
<AMapDisplay 
  location={{ lng: 116.397428, lat: 39.90923 }}
  address="北京市东城区王府井大街1号"
  title="门店位置"
/>
```

## 优化策略

### 1. 防止DOM引用错误
- 使用唯一ID引用DOM元素，避免React DOM操作与地图API冲突
```javascript
const mapElementId = useRef(`map-container-${Math.random().toString(36).substring(2, 9)}`);
```

### 2. 安全销毁地图实例
```javascript
const safeDestroyMap = () => {
  if (map) {
    try {
      // 先移除标记
      if (marker && marker.getMap()) {
        marker.setMap(null);
      }
      
      // 清除所有事件监听
      map.clearEvents();
      
      // 清除所有覆盖物
      map.clearMap();
      
      // 销毁地图实例
      map.destroy();
    } catch (e) {
      console.error('Error destroying map:', e);
    }
  }
};
```

### 3. 延迟初始化
确保DOM元素已完全渲染后再创建地图实例：
```javascript
// 使用延迟确保DOM已完全渲染
await new Promise(resolve => setTimeout(resolve, 500));
```

### 4. 自动重试机制
当地图加载失败时自动重试：
```javascript
// 延迟后自动重试
setTimeout(() => {
  if (mapLoadAttempts.current < 3) {
    console.log(`Retrying map initialization (attempt ${mapLoadAttempts.current + 1}/3)...`);
    initMap();
  }
}, 2000);
```

## 常见问题与解决方案

### 1. "AMap or AMap.Map is undefined" 错误
**原因**：地图API未成功加载或在DOM准备好之前尝试创建地图实例
**解决方案**：
- 确保正确设置安全配置
- 使用延迟加载策略
- 检查API是否已全局可用

### 2. "Failed to execute 'removeChild' on 'Node'" 错误
**原因**：React DOM操作与地图API DOM操作冲突
**解决方案**：
- 使用唯一ID引用DOM元素
- 确保地图实例在组件卸载前正确销毁
- 改进地图组件的生命周期管理

### 3. 安全配置问题
**原因**：未正确设置安全密钥或设置时机不正确
**解决方案**：
- 在HTML头部添加安全配置
- 确保在加载地图API前设置安全配置
- 使用正确的安全密钥

## 最佳实践

1. **始终使用安全销毁方法**
   在组件卸载前正确清理地图实例和相关资源

2. **使用唯一DOM ID**
   避免React组件重渲染时的DOM引用问题

3. **优先使用全局AMap实例**
   如果全局已有AMap对象，优先使用现有实例而不是重新加载

4. **完善的错误处理**
   实现自动重试机制和用户友好的错误提示

5. **预加载策略**
   在HTML头部预加载地图API，减少组件内加载时间

## 变更记录

- **v1.4**: 
  - 添加安全配置和正确密钥
  - 实现更强大的AMapLocationPicker组件
  - 修复DOM节点引用错误
  - 添加自动重试机制
  - 优化地图加载流程 