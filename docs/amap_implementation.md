# AMap 模块实现文档

## 概述

宠物零售竞品分析系统的高德地图实现采用了集中式配置管理方法，包含了健壮的错误处理和生命周期管理。此架构专门设计用于解决之前出现的问题，包括"Map container element not found"错误和"Failed to execute 'removeChild' on 'Node'"错误。

## 版本信息

- **当前版本**: v1.5
- **更新时间**: 2024年7月
- **主要改进**: 解决DOM节点管理冲突，增强地图实例生命周期管理

## 核心架构组件

### 1. 集中式地图配置 (`mapConfig.js`)

系统使用中央配置模块 (`mapConfig.js`) 实现：

- **单例模式**加载AMap API，确保在整个应用中只加载一次
- **实例跟踪**管理应用中的所有地图实例
- 地图初始化失败时的**重试机制**
- 适当的**清理函数**防止内存泄漏

关键特性包括：

```javascript
// API密钥和安全配置
export const AMAP_KEY = 'da2ed03e1fd1b75105f23957ba77c803';
export const AMAP_SECURITY_CODE = '953ce94c1e646e2898a74576983fd9d7';

// 默认设置
export const DEFAULT_CENTER = [104.065837, 30.657349]; // 成都天府广场
export const DEFAULT_ZOOM = 12;

// 跟踪地图实例
const mapInstances = {};
```

### 2. 地图组件设计

系统实现了两个主要的地图组件：

#### `AMapDisplay.js`
一个只读组件，用于查看位置，特点：
- 显示位置，可选标题和地址信息
- 当精确坐标不可用时回退到地理编码
- 具有重新加载功能的错误处理

#### `AMapLocationPicker.js`
一个交互式组件，允许用户：
- 通过地址搜索位置
- 通过点击地图选择位置
- 获取用户当前位置
- 拖动标记以微调定位
- 反向地理编码选定位置以填充地址字段

两个组件都使用：
- **固定容器ID前缀+随机后缀**以确保DOM引用一致性
- **useLayoutEffect**用于同步DOM操作
- **useEffect**带有适当清理用于异步操作
- **useRef**跟踪地图和标记实例

### 3. 错误处理与恢复

实现包括健壮的错误处理：

```javascript
// 初始化失败的重试逻辑
export const initMapInstance = async (containerId, options = {}, maxRetries = 3, retryDelay = 300) => {
  let retries = 0;
  
  // 如果实例已存在，先销毁
  destroyMapInstance(containerId);
  
  const tryInitMap = async () => {
    try {
      // 检查容器是否存在
      const container = getMapContainer(containerId);
      if (!container) {
        if (retries < maxRetries) {
          console.log(`Map container ${containerId} not found, retrying (${retries + 1}/${maxRetries})...`);
          retries++;
          return new Promise(resolve => setTimeout(() => resolve(tryInitMap()), retryDelay));
        } else {
          throw new Error(`Map container element not found: ${containerId}`);
        }
      }
      
      // 加载AMap API并初始化地图...
    } catch (error) {
      // 失败时重试
      if (retries < maxRetries) {
        console.log(`Failed to initialize map, retrying (${retries + 1}/${maxRetries})...`);
        retries++;
        return new Promise(resolve => setTimeout(() => resolve(tryInitMap()), retryDelay));
      } else {
        throw error;
      }
    }
  };
  
  return tryInitMap();
};
```

### 4. 清理机制

系统实现了适当的清理以防止内存泄漏：

```javascript
export const destroyMapInstance = (containerId) => {
  if (mapInstances[containerId]) {
    try {
      const instance = mapInstances[containerId];
      
      // 清除所有事件和覆盖物
      instance.clearEvents();
      instance.clearMap();
      
      // 销毁地图实例
      instance.destroy();
      
      // 从跟踪列表中移除
      delete mapInstances[containerId];
      
      console.log(`Map instance for container ${containerId} destroyed`);
    } catch (error) {
      console.error(`Error destroying map instance for container ${containerId}:`, error);
    }
    
    // 尝试清理DOM元素，以防React DOM管理冲突
    try {
      const container = document.getElementById(containerId);
      if (container) {
        // 备份父节点引用，以防React已经移除了父节点
        const parent = container.parentNode;
        
        // 从父节点中移除自身，避免React的removeChild操作
        if (parent) {
          parent.removeChild(container);
        }
      }
    } catch (e) {
      console.error(`Error cleaning up map container ${containerId}:`, e);
    }
  }
};
```

## 集成到父组件

地图组件在三个主要页面中使用：

### 1. `AddStore.js`
- 使用带有静态key ("add-store-map-fixed") 的 `AMapLocationPicker` 进行位置选择
- 当选择位置时，通过回调处理程序更新表单值

### 2. `EditStore.js`
- 使用带有基于ID的静态key ("edit-store-map-{id}") 的 `AMapLocationPicker`
- 使用现有门店位置数据初始化地图
- 在回调中处理位置更新

### 3. `ViewStore.js` 
- 使用 `AMapDisplay` 以只读模式显示门店位置
- 如果坐标不可用，回退到地址地理编码

## 主要改进

1. **DOM元素管理**:
   - 使用基于固定前缀+随机后缀的容器ID而不是动态生成的
   - 添加重试逻辑处理初始化计时问题
   - 使用带有回退的适当DOM元素查找 `getElementById`

2. **实例管理**:
   - 维护地图实例的引用
   - 在组件卸载前适当清理实例
   - 使用refs跟踪组件内的标记和地图实例

3. **生命周期管理**:
   - 使用 `useLayoutEffect` 进行影响DOM的同步操作
   - 使用带有适当清理函数的 `useEffect`
   - 添加超时确保DOM在初始化前准备就绪

4. **错误恢复**:
   - 为初始化失败添加重试逻辑
   - 提供用户友好的错误消息
   - 包含重置/重新加载选项

## 表单管理集成

地图组件与表单管理良好集成：
- `handleLocationSelect` 在选择位置时更新表单值
- `handleAddressChange` 基于反向地理编码结果更新地址字段
- 初始值用于使用现有数据初始化地图

## 已解决的问题

1. **"Map container element not found" 错误**
   - 通过改进的容器创建和引用跟踪解决
   - 实现了重试机制以处理DOM加载时间差异

2. **"Failed to execute 'removeChild' on 'Node'" 错误**
   - 通过改进DOM生命周期管理解决
   - 实现了多层嵌套容器架构
   - 在组件卸载时适当清理DOM节点

## 未来改进

未来增强的潜在领域：

1. 为地理编码结果实现缓存以减少API调用
2. 添加更高级的地图控件用于测量距离或绘制区域
3. 实现聚类以在单个地图上显示多个门店位置
4. 添加自定义地图样式以更好地匹配应用程序的设计 