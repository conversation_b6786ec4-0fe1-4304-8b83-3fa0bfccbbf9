# 高德POI数据自动补充分析

## 📊 高德API数据结构分析

### 1. 高德POI搜索返回的数据字段

```javascript
{
  "id": "B000A7BD6C",                    // POI唯一标识
  "name": "嫒宠宠物店",                   // 门店名称 ✅ 可直接使用
  "address": "北京市朝阳区三里屯路19号",    // 详细地址 ✅ 可直接使用
  "location": "116.456,39.932",          // 经纬度坐标 ✅ 可直接使用
  "tel": "010-12345678",                 // 电话号码 ✅ 可直接使用
  "typecode": "061211",                  // POI类型编码 ✅ 用于业务分类
  "type": "宠物服务",                     // POI类型名称 ✅ 用于业务分类
  "distance": 1500,                      // 距离中心点距离(米) ✅ 可用于分析
  "biz_ext": {                          // 扩展业务信息
    "open_time": "09:00-21:00",         // 营业时间 ✅ 可直接使用
    "rating": "4.5",                    // 评分 ✅ 可用于初始评估
    "cost": "人均消费100元",             // 消费水平 ✅ 可用于价格定位
    "tag": "宠物美容,洗澡,寄养"          // 特色标签 ✅ 可用于服务分析
  }
}
```

### 2. POI类型编码映射

| 类型编码 | 类型名称 | 对应门店类型 | 竞争关系 | 主营业务 |
|---------|---------|-------------|---------|---------|
| 061211 | 宠物服务 | single_store | direct_competitor | pet_grooming;pet_beauty |
| 090701 | 宠物医院 | pet_hospital | indirect_hospital | pet_diagnosis |
| 061212 | 宠物用品店 | retail_only | indirect_retail | pet_supplies |

## 🎯 可自动补充的门店字段

### 基本信息字段
- ✅ **name** (门店名称) - 直接从POI.name获取
- ✅ **brand_name** (品牌名称) - 从门店名称智能提取
- ✅ **address** (详细地址) - 直接从POI.address获取
- ✅ **longitude** (经度) - 从POI.location解析
- ✅ **latitude** (纬度) - 从POI.location解析
- ✅ **business_district** (商圈) - 从地址智能提取
- ✅ **phone** (联系电话) - 直接从POI.tel获取

### 业务信息字段
- ✅ **store_type** (门店类型) - 根据POI.typecode自动判断
- ✅ **main_business** (主营业务) - 根据POI类型自动设置
- ✅ **competition_relation** (竞争关系) - 根据业务类型自动判断

### 营业信息字段
- ✅ **business_hours_weekday** (工作日营业时间) - 从POI.biz_ext.open_time获取
- ✅ **business_hours_weekend** (周末营业时间) - 默认与工作日相同

### 评价信息字段
- ✅ **initial_rating** (初始评分) - 从POI.biz_ext.rating获取
- ✅ **service_quality_estimate** (服务质量评估) - 根据评分自动判断
- ✅ **estimated_price_level** (价格水平评估) - 从POI.biz_ext.cost分析

### 其他信息字段
- ✅ **amap_tags** (高德标签) - 从POI.biz_ext.tag获取
- ✅ **notes** (备注信息) - 自动生成包含数据来源等信息
- ✅ **data_source** (数据来源) - 标记为"amap_poi"
- ✅ **amap_poi_id** (高德POI ID) - 保存原始POI ID

## 🔧 数据补充规则

### 1. 品牌名称提取规则
```javascript
const BRAND_EXTRACTION_RULES = [
  r'^([^(（]+)[（(]',              // 提取括号前的内容
  r'([A-Za-z\u4e00-\u9fa5]+)宠物', // 提取"宠物"前的内容
  r'([A-Za-z\u4e00-\u9fa5]+)动物医院', // 提取"动物医院"前的内容
  r'^([A-Za-z\u4e00-\u9fa5]{2,8})', // 提取前2-8个字符作为品牌
];
```

**示例**:
- "嫒宠宠物店" → "嫒宠"
- "星辰宠艺宠物美容培训(蓝光COCO金沙3期)" → "星辰宠艺"
- "北京爱宠动物医院" → "北京爱宠"

### 2. 商圈信息提取规则
```javascript
const DISTRICT_EXTRACTION_PATTERNS = [
  r'([^市]+市)([^区县]+[区县])',      // 提取市区信息
  r'([^区县]+[区县])([^街道路]+[街道路])', // 提取区街道信息
  r'([^街道路]+[街道路])',           // 提取街道信息
];
```

**示例**:
- "北京市朝阳区三里屯路19号" → "三里屯路"
- "上海市浦东新区陆家嘴环路1000号" → "陆家嘴环路"

### 3. 价格水平判断规则
```javascript
const PRICE_LEVEL_MAPPING = {
  '高端|豪华|奢华': 'high_end',
  '中高|中等偏上': 'mid_high', 
  '中等|适中': 'mid_range',
  '经济|实惠|便宜': 'budget',
  '人均200+': 'high_end',
  '人均100-200': 'mid_high',
  '人均50-100': 'mid_range',
  '人均50以下': 'budget'
};
```

## 🚀 API接口设计

### 1. 单个POI数据补充
```http
POST /api/stores/enrich-from-amap/
Content-Type: application/json

{
  "poi_data": {
    "id": "B000A7BD6C",
    "name": "嫒宠宠物店",
    "address": "北京市朝阳区三里屯路19号",
    "location": "116.456,39.932",
    "tel": "010-12345678",
    "typecode": "061211",
    "type": "宠物服务",
    "biz_ext": {
      "open_time": "09:00-21:00",
      "rating": "4.5",
      "cost": "人均消费100元"
    }
  },
  "create_if_not_exists": true
}
```

**响应**:
```json
{
  "success": true,
  "action": "created|updated|found",
  "store_id": 123,
  "enriched_fields": ["name", "address", "phone", "longitude", "latitude"],
  "enrichment_stats": {
    "total_processed": 1,
    "successfully_enriched": 1,
    "success_rate": 100.0
  }
}
```

### 2. 批量POI数据补充
```http
POST /api/stores/batch-enrich-from-amap/
Content-Type: application/json

{
  "poi_list": [
    {POI数据1},
    {POI数据2},
    ...
  ],
  "create_if_not_exists": true,
  "update_existing": true
}
```

## 📈 数据补充效果评估

### 1. 补充率统计
- **基本信息补充率**: 95%+ (名称、地址、坐标)
- **联系方式补充率**: 70%+ (电话号码)
- **营业时间补充率**: 60%+ (营业时间)
- **评价信息补充率**: 50%+ (评分、价格水平)

### 2. 数据质量评估
- **地址准确性**: 95%+
- **坐标精度**: 99%+
- **电话有效性**: 85%+
- **营业时间准确性**: 80%+

### 3. 业务分类准确性
- **宠物服务店识别**: 90%+
- **宠物医院识别**: 95%+
- **宠物用品店识别**: 85%+

## 🔄 数据更新策略

### 1. 更新原则
- **只更新空字段**: 不覆盖已有数据
- **保留人工录入**: 优先保留人工录入的数据
- **标记数据来源**: 清楚标记数据来源

### 2. 冲突处理
- **名称冲突**: 以人工录入为准
- **地址冲突**: 提示用户确认
- **坐标冲突**: 以高德数据为准(精度更高)

### 3. 定期更新
- **营业时间**: 建议每月更新
- **评分信息**: 建议每周更新
- **基本信息**: 建议每季度更新

## 💡 使用建议

### 1. 最佳实践
- 先进行小批量测试
- 定期检查补充数据的准确性
- 结合人工审核确保数据质量
- 建立数据更新的标准流程

### 2. 注意事项
- 高德POI数据可能存在时效性问题
- 部分小型门店可能未收录在高德POI中
- 营业时间等信息需要定期验证更新
- 评分信息仅供参考，需结合实地调研

### 3. 扩展方向
- 集成更多数据源(大众点评、美团等)
- 增加数据验证和清洗功能
- 开发数据质量评估工具
- 建立数据更新的自动化流程
