#!/bin/bash
# 生产环境打包脚本 - v1.7.2
echo "=== 开始准备生产环境代码 ===" && echo "版本: v1.7.2" && echo ""
TEMP_DIR="temp_prod_v1.7.2" && echo "创建临时目录: $TEMP_DIR" && rm -rf $TEMP_DIR && mkdir -p $TEMP_DIR
echo "复制前端源代码..." && cp -r frontend $TEMP_DIR/ && cp -r backend $TEMP_DIR/ && cp -r mini_app $TEMP_DIR/
echo "移除所有调试日志语句..." && find $TEMP_DIR -type f -name "*.js" -exec sed -i "" -E "/console\.(log|error|warn|debug)\(/d" {} \;
VERSION="v1.7.2" && RELEASE_DIR="releases" && mkdir -p $RELEASE_DIR && TIMESTAMP=$(date +"%Y%m%d%H%M%S") && PACKAGE_NAME="pet_competitor_system-${VERSION}-release-${TIMESTAMP}.tar.gz"
echo "正在创建生产部署包..." && tar -czf $RELEASE_DIR/$PACKAGE_NAME --exclude="node_modules" --exclude=".git" $TEMP_DIR
echo "" && echo "=== 生产环境包准备完成 ===" && echo "打包文件: $RELEASE_DIR/$PACKAGE_NAME" && echo "" && echo "清理临时文件..." && rm -rf $TEMP_DIR && echo "完成!"
