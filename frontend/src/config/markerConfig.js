/**
 * 地图标记图标配置
 * 提供多种图标方案，支持在线图标和本地图标
 */

// 高德地图在线图标方案（推荐，稳定可靠）
export const AMAP_ONLINE_ICONS = {
  default: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',        // 蓝色
  direct_competitor: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png', // 红色
  indirect_hospital: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_y.png', // 黄色
  indirect_retail: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_g.png',   // 绿色
  potential_competitor: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_o.png', // 橙色
  non_competitor: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_grey.png', // 灰色
  unknown: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png',           // 未知 - 蓝色
  center_point: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_r.png',      // 中心点 - 红色
};

// 本地SVG图标方案（如果需要自定义图标）
export const LOCAL_SVG_ICONS = {
  default: '/markers/ditubiaodian.svg',
  direct_competitor: '/markers/ditubiaodian_1.svg',
  indirect_hospital: '/markers/ditubiaodian_2.svg',
  indirect_retail: '/markers/ditubiaodian_3.svg',
  potential_competitor: '/markers/ditubiaodian_4.svg',
  non_competitor: '/markers/ditubiaodian_5.svg',
  unknown: '/markers/ditubiaodian.svg',
  center_point: '/markers/ditubiaodian-fa.svg',
};

// 竞争关系颜色配置
export const COMPETITION_COLORS = {
  direct_competitor: '#ff4d4f',      // 红色
  indirect_hospital: '#faad14',      // 黄色
  indirect_retail: '#52c41a',        // 绿色
  potential_competitor: '#fa8c16',   // 橙色
  non_competitor: '#8c8c8c',         // 灰色
  unknown: '#1890ff',                // 蓝色
};

// 竞争关系中文名称
export const COMPETITION_LABELS = {
  direct_competitor: '直接竞争对手',
  indirect_hospital: '间接竞争对手-宠物医院',
  indirect_retail: '间接竞争对手-纯零售',
  potential_competitor: '潜在竞争对手',
  non_competitor: '非竞争对手',
  unknown: '未知',
};

// 图标配置类
export class MarkerIconConfig {
  constructor(iconType = 'online') {
    this.iconType = iconType;
    this.icons = iconType === 'local' ? LOCAL_SVG_ICONS : AMAP_ONLINE_ICONS;
  }

  /**
   * 根据竞争关系获取图标URL
   * @param {string} competitionRelation 竞争关系
   * @returns {string} 图标URL
   */
  getIconUrl(competitionRelation) {
    return this.icons[competitionRelation] || this.icons.default;
  }

  /**
   * 根据竞争关系获取颜色
   * @param {string} competitionRelation 竞争关系
   * @returns {string} 颜色值
   */
  getColor(competitionRelation) {
    return COMPETITION_COLORS[competitionRelation] || COMPETITION_COLORS.unknown;
  }

  /**
   * 根据竞争关系获取中文标签
   * @param {string} competitionRelation 竞争关系
   * @returns {string} 中文标签
   */
  getLabel(competitionRelation) {
    return COMPETITION_LABELS[competitionRelation] || COMPETITION_LABELS.unknown;
  }

  /**
   * 创建高德地图标记图标
   * @param {Object} AMap 高德地图API对象
   * @param {string} competitionRelation 竞争关系
   * @param {Object} options 图标选项
   * @returns {Object} 高德地图图标对象
   */
  createAMapIcon(AMap, competitionRelation, options = {}) {
    const defaultOptions = {
      size: new AMap.Size(32, 32),
      imageSize: new AMap.Size(32, 32),
      imageOffset: new AMap.Pixel(0, 0),
    };

    const finalOptions = { ...defaultOptions, ...options };
    
    return new AMap.Icon({
      size: finalOptions.size,
      image: this.getIconUrl(competitionRelation),
      imageSize: finalOptions.imageSize,
      imageOffset: finalOptions.imageOffset,
    });
  }

  /**
   * 创建高德地图标记
   * @param {Object} AMap 高德地图API对象
   * @param {Object} options 标记选项
   * @returns {Object} 高德地图标记对象
   */
  createAMapMarker(AMap, options = {}) {
    const {
      position,
      competitionRelation = 'default',
      title = '',
      zIndex = 100,
      iconOptions = {},
      ...markerOptions
    } = options;

    const icon = this.createAMapIcon(AMap, competitionRelation, iconOptions);
    
    return new AMap.Marker({
      position,
      title,
      icon,
      zIndex,
      offset: new AMap.Pixel(-16, -16), // 居中偏移
      ...markerOptions
    });
  }

  /**
   * 获取所有可用的竞争关系类型
   * @returns {Array} 竞争关系类型数组
   */
  getAvailableCompetitionTypes() {
    return Object.keys(COMPETITION_LABELS).map(key => ({
      value: key,
      label: COMPETITION_LABELS[key],
      color: COMPETITION_COLORS[key],
      icon: this.getIconUrl(key)
    }));
  }
}

// 默认图标配置实例
export const defaultMarkerConfig = new MarkerIconConfig('online');

// 导出常用方法
export const getMarkerIcon = (competitionRelation) => {
  return defaultMarkerConfig.getIconUrl(competitionRelation);
};

export const getMarkerColor = (competitionRelation) => {
  return defaultMarkerConfig.getColor(competitionRelation);
};

export const getMarkerLabel = (competitionRelation) => {
  return defaultMarkerConfig.getLabel(competitionRelation);
};

export const createMarker = (AMap, options) => {
  return defaultMarkerConfig.createAMapMarker(AMap, options);
};
