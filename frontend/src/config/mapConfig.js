/**
 * 高德地图全局配置
 */
import AMapLoader from '@amap/amap-jsapi-loader';

// 高德地图API密钥
export const AMAP_KEY = 'da2ed03e1fd1b75105f23957ba77c803';

// 高德地图安全密钥
export const AMAP_SECURITY_CODE = '953ce94c1e646e2898a74576983fd9d7';

// 高德地图API版本
export const AMAP_VERSION = '2.0';

// 高德地图默认插件
export const AMAP_PLUGINS = [
  'AMap.Geocoder',
  'AMap.PlaceSearch',
  'AMap.ToolBar',
  'AMap.Geolocation',
  'AMap.AutoComplete',
  'AMap.Scale',
  'AMap.OverView',
  'AMap.ToolBar',
  'AMap.MapType',
  'AMap.CircleEditor',
  'AMap.MouseTool',
];

// 默认地图中心点（成都天府广场）
// 默认地图中心点（中国中心）
//export const DEFAULT_CENTER = [116.397428, 39.90923];

export const DEFAULT_CENTER = [104.065837, 30.657349];

// 默认缩放级别
export const DEFAULT_ZOOM = 12;

// 高德地图配置对象
export const AMAP_CONFIG = {
  key: AMAP_KEY,
  version: AMAP_VERSION,
  plugins: AMAP_PLUGINS,
  securityJsCode: AMAP_SECURITY_CODE,
};

// 跟踪已创建的地图实例
const mapInstances = {};

// AMap API实例缓存
let cachedAMapAPI = null;
// 跟踪API加载状态的Promise
let loadingPromise = null;

/**
 * 初始化高德地图安全配置
 * 应在应用启动时调用
 */
export const initMapSecurityConfig = () => {
  if (!window._AMapSecurityConfig || window._AMapSecurityConfig.securityJsCode !== AMAP_SECURITY_CODE) {
    window._AMapSecurityConfig = { securityJsCode: AMAP_SECURITY_CODE };
    console.log('AMap security configuration initialized');
  }
};

/**
 * 加载高德地图API（单例模式）
 * 确保整个应用中只加载一次AMap
 */
export const loadAMapAPI = async () => {
  try {
    // 如果之前已经触发了加载但尚未完成，返回正在进行的Promise
    if (loadingPromise) {
      return loadingPromise;
    }
    
    // 如果AMap已经在window对象上可用，直接返回
    if (window.AMap && window.AMap.Map) {
      console.log('Using existing AMap API from window');
      return window.AMap;
    }
    
    // 如果之前已经成功加载过API，使用缓存的实例
    if (cachedAMapAPI) {
      console.log('Using cached AMap API reference');
      return cachedAMapAPI;
    }
    
    // 确保安全配置已初始化
    initMapSecurityConfig();
    
    // 创建新的加载Promise
    loadingPromise = new Promise(async (resolve, reject) => {
      try {
        console.log('Loading AMap API with key:', AMAP_KEY);
        const AMap = await AMapLoader.load(AMAP_CONFIG);
        
        // 缓存API实例
        cachedAMapAPI = AMap;
        console.log('AMap API loaded successfully');
        
        resolve(AMap);
      } catch (error) {
        console.error('Failed to load AMap API:', error);
        reject(error);
      } finally {
        // 完成后重置加载Promise
        loadingPromise = null;
      }
    });
    
    return loadingPromise;
  } catch (error) {
    console.error('Failed to load AMap API:', error);
    loadingPromise = null;
    throw error;
  }
};

/**
 * 检查DOM容器是否存在
 * @param {string} containerId - 地图容器DOM元素的ID
 * @returns {HTMLElement|null} 存在返回容器元素，不存在返回null
 */
const getMapContainer = (containerId) => {
  // 首先尝试使用getElementById
  let container = document.getElementById(containerId);
  
  // 如果找不到，可能是因为组件重新渲染，尝试使用选择器查找
  if (!container) {
    container = document.querySelector(`#${containerId}`);
  }
  
  return container;
};

/**
 * 安全销毁地图实例
 * @param {string} containerId - 地图容器DOM元素的ID 
 */
export const destroyMapInstance = (containerId) => {
  if (mapInstances[containerId]) {
    try {
      const instance = mapInstances[containerId];
      
      // 清除所有事件和覆盖物
      instance.clearEvents();
      instance.clearMap();
      
      // 销毁地图实例
      instance.destroy();
      
      // 从跟踪列表中移除
      delete mapInstances[containerId];
      
      console.log(`Map instance for container ${containerId} destroyed`);
    } catch (error) {
      console.error(`Error destroying map instance for container ${containerId}:`, error);
    }
    
    // 不再尝试直接删除DOM节点，让React自行处理DOM更新
    // 这样可以避免"Failed to execute 'removeChild' on 'Node'"错误
  }
};

/**
 * 初始化地图实例
 * @param {string} containerId - 地图容器DOM元素的ID
 * @param {Object} options - 地图初始化选项
 * @param {number} maxRetries - 最大重试次数
 * @param {number} retryDelay - 重试间隔时间(ms)
 */
export const initMapInstance = async (containerId, options = {}, maxRetries = 3, retryDelay = 300) => {
  let retries = 0;
  
  // 如果已存在实例，先销毁
  destroyMapInstance(containerId);
  
  const tryInitMap = async () => {
    try {
      // 检查容器是否存在
      const container = getMapContainer(containerId);
      if (!container) {
        if (retries < maxRetries) {
          console.log(`Map container ${containerId} not found, retrying (${retries + 1}/${maxRetries})...`);
          retries++;
          return new Promise(resolve => setTimeout(() => resolve(tryInitMap()), retryDelay));
        } else {
          throw new Error(`Map container element not found: ${containerId}`);
        }
      }
      
      // 加载AMap API
      const AMap = await loadAMapAPI();
      
      // 合并默认选项与用户选项
      const mapOptions = {
        zoom: DEFAULT_ZOOM,
        center: DEFAULT_CENTER,
        ...options
      };
      
      // 创建地图实例
      console.log(`Creating map instance for container ${containerId}`);
      const mapInstance = new AMap.Map(containerId, mapOptions);
      
      // 记录地图实例以便后续管理
      mapInstances[containerId] = mapInstance;
      
      return {
        AMap,
        map: mapInstance
      };
    } catch (error) {
      if (retries < maxRetries) {
        console.log(`Failed to initialize map, retrying (${retries + 1}/${maxRetries})...`);
        retries++;
        return new Promise(resolve => setTimeout(() => resolve(tryInitMap()), retryDelay));
      } else {
        throw error;
      }
    }
  };
  
  return tryInitMap();
};

/**
 * 生成唯一的地图容器ID
 */
export const generateMapContainerId = (prefix = 'map-container') => {
  return `${prefix}-${Math.random().toString(36).substring(2, 9)}`;
};

export default {
  AMAP_KEY,
  AMAP_SECURITY_CODE,
  AMAP_VERSION,
  AMAP_PLUGINS,
  DEFAULT_CENTER,
  DEFAULT_ZOOM,
  AMAP_CONFIG,
  initMapSecurityConfig,
  loadAMapAPI,
  initMapInstance,
  destroyMapInstance,
  generateMapContainerId,
}; 