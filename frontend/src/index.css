body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.tab-info-message {
  padding: 24px;
  text-align: center;
}

.tab-info-message p {
  margin-bottom: 16px;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.65);
}

.ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.site-page-header {
  padding: 16px 24px;
  background: #fff;
  margin-bottom: 16px;
}

.required-field::after {
  content: ' *';
  color: #ff4d4f;
}

.custom-tag {
  margin-right: 8px;
  margin-bottom: 8px;
}

.custom-card-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
}

.field-label {
  color: rgba(0, 0, 0, 0.65);
  font-size: 14px;
  margin-bottom: 4px;
}

.field-value {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  margin-bottom: 16px;
}
