import axios from 'axios';

// 高德Web服务API密钥
const WEB_API_KEY = 'e34a7c089ef9ef7da8e0c514fa6de87a';
const API_BASE_URL = 'https://restapi.amap.com/v3';

// 创建axios实例
const amapApiClient = axios.create({
  baseURL: API_BASE_URL,
  params: {
    key: WEB_API_KEY,
    output: 'JSON'
  }
});

/**
 * 周边搜索POI
 * @param {number} lng - 中心点经度
 * @param {number} lat - 中心点纬度
 * @param {number} radius - 搜索半径（米）
 * @param {string} types - POI类型编码，多个类型用"|"分隔
 * @param {number} offset - 每页记录数，最大25
 * @param {number} page - 当前页数
 * @returns {Promise<Object>} - 响应结果
 */
export const searchNearbyPOIs = async (lng, lat, radius, types, offset = 25, page = 1) => {
  try {
    const response = await amapApiClient.get('/place/around', {
      params: {
        location: `${lng},${lat}`,
        radius,
        types,
        offset,
        page,
        extensions: 'all'
      }
    });
    
    if (response.data.status === '1') {
      return response.data;
    } else {
      console.error('高德API请求错误:', response.data.info);
      throw new Error(`高德API请求错误: ${response.data.info}`);
    }
  } catch (error) {
    console.error('高德周边搜索失败:', error);
    throw error;
  }
};

/**
 * 关键字搜索POI
 * @param {string} keywords - 关键字
 * @param {string} types - POI类型编码，多个类型用"|"分隔
 * @param {string} city - 城市名，可选
 * @param {string} citylimit - 是否限制在当前城市内，默认为true
 * @param {number} offset - 每页记录数，最大25
 * @param {number} page - 当前页数
 * @returns {Promise<Object>} - 响应结果
 */
export const searchPOIsByKeyword = async (keywords, types, city = '', citylimit = true, offset = 25, page = 1) => {
  try {
    const response = await amapApiClient.get('/place/text', {
      params: {
        keywords,
        types,
        city,
        citylimit: citylimit ? 'true' : 'false',
        offset,
        page,
        extensions: 'all'
      }
    });
    
    if (response.data.status === '1') {
      return response.data;
    } else {
      console.error('高德API请求错误:', response.data.info);
      throw new Error(`高德API请求错误: ${response.data.info}`);
    }
  } catch (error) {
    console.error('高德关键字搜索失败:', error);
    throw error;
  }
};

/**
 * 地理编码（地址转坐标）
 * @param {string} address - 地址
 * @param {string} city - 城市名，可选
 * @returns {Promise<Object>} - 响应结果
 */
export const geocode = async (address, city = '') => {
  try {
    const response = await amapApiClient.get('/geocode/geo', {
      params: {
        address,
        city
      }
    });
    
    if (response.data.status === '1') {
      return response.data;
    } else {
      console.error('高德API请求错误:', response.data.info);
      throw new Error(`高德API请求错误: ${response.data.info}`);
    }
  } catch (error) {
    console.error('高德地理编码失败:', error);
    throw error;
  }
};

export default {
  searchNearbyPOIs,
  searchPOIsByKeyword,
  geocode
}; 