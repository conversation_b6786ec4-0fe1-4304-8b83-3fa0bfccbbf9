import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { initMapSecurityConfig, AMAP_SECURITY_CODE } from './config/mapConfig';

// 在应用加载前初始化高德地图安全配置
console.log('Initializing AMap security config...');
initMapSecurityConfig();

// 提前确保window._AMapSecurityConfig已设置，防止任何潜在问题
if (!window._AMapSecurityConfig) {
  window._AMapSecurityConfig = { 
    securityJsCode: AMAP_SECURITY_CODE 
  };
  console.log('AMap security config manually set in index.js');
}

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
