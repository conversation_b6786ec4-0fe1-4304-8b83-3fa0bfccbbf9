import React, { useState, useEffect } from 'react';
import { 
  Table, Button, Modal, Input, Space, Card, message, 
  Tag, Switch, Popconfirm, Select, Pagination, Form, Row, Col 
} from 'antd';
import { 
  UserOutlined, LockOutlined, DeleteOutlined, 
  EditOutlined, SearchOutlined, FilterOutlined 
} from '@ant-design/icons';
import moment from 'moment';
import axios from 'axios';
import { USER_API } from '../config/api.config';

const { Option } = Select;
const { confirm } = Modal;

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchKeyword, setSearchKeyword] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [passwordModalVisible, setPasswordModalVisible] = useState(false);
  const [selectedUser, setSelectedUser] = useState(null);
  const [newPassword, setNewPassword] = useState('');

  // 获取用户列表
  const fetchUsers = async (page = 1, search = '', status = 'all', role = 'all') => {
    setLoading(true);
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        message.error('请先登录');
        return;
      }

      let url = USER_API.ALL_USERS;
      
      // 构建查询参数
      const params = new URLSearchParams();
      if (page) params.append('page', page);
      if (search) params.append('search', search);
      if (status !== 'all') params.append('status', status);
      if (role !== 'all') params.append('role', role);
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await axios.get(url, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      // 检查是否有分页信息
      if (response.data.count !== undefined) {
        setPagination({
          ...pagination,
          current: page,
          total: response.data.count
        });
        setUsers(response.data.results || []);
      } else {
        // 如果没有分页信息，直接使用返回的数组
        setUsers(response.data || []);
        setPagination({
          ...pagination,
          current: 1,
          total: response.data.length
        });
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件加载时获取数据
  useEffect(() => {
    fetchUsers();
  }, []);

  // 处理搜索
  const handleSearch = () => {
    fetchUsers(1, searchKeyword, statusFilter, roleFilter);
  };

  // 处理状态筛选
  const handleStatusFilterChange = (value) => {
    setStatusFilter(value);
    fetchUsers(1, searchKeyword, value, roleFilter);
  };

  // 处理角色筛选
  const handleRoleFilterChange = (value) => {
    setRoleFilter(value);
    fetchUsers(1, searchKeyword, statusFilter, value);
  };

  // 处理分页变化
  const handlePageChange = (page) => {
    fetchUsers(page, searchKeyword, statusFilter, roleFilter);
  };

  // 切换用户激活状态
  const handleToggleActive = async (user) => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        message.error('请先登录');
        return;
      }

      const response = await axios.post(
        USER_API.TOGGLE_ACTIVE(user.id),
        {},
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      if (response.data.message) {
        message.success(response.data.message);
        // 刷新用户列表
        fetchUsers(pagination.current, searchKeyword, statusFilter, roleFilter);
      }
    } catch (error) {
      console.error('操作失败:', error);
      message.error(error.response?.data?.error || '操作失败，请稍后重试');
    }
  };

  // 删除用户
  const handleDeleteUser = async (user) => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        message.error('请先登录');
        return;
      }

      const response = await axios.delete(
        USER_API.DELETE_USER(user.id),
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      message.success('用户已成功删除');
      // 刷新用户列表
      fetchUsers(pagination.current, searchKeyword, statusFilter, roleFilter);
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error(error.response?.data?.error || '删除用户失败，请稍后重试');
    }
  };

  // 显示修改密码对话框
  const showPasswordModal = (user) => {
    setSelectedUser(user);
    setNewPassword('');
    setPasswordModalVisible(true);
  };

  // 处理修改密码
  const handleChangePassword = async () => {
    if (!newPassword) {
      message.error('请输入新密码');
      return;
    }

    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        message.error('请先登录');
        return;
      }

      const response = await axios.post(
        USER_API.CHANGE_PASSWORD(selectedUser.id),
        { new_password: newPassword },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      message.success('密码已成功修改');
      setPasswordModalVisible(false);
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error(error.response?.data?.error || '修改密码失败，请稍后重试');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      render: (text, record) => (
        <span>
          <UserOutlined style={{ marginRight: 8 }} />
          {text}
        </span>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email'
    },
    {
      title: '姓名',
      key: 'name',
      render: (_, record) => (
        <span>{`${record.first_name || ''} ${record.last_name || ''}`}</span>
      )
    },
    {
      title: '角色',
      key: 'role',
      render: (_, record) => {
        const role = record.profile?.role || 'user';
        let color;
        
        switch (role) {
          case 'admin':
            color = 'red';
            break;
          case 'staff':
            color = 'blue';
            break;
          default:
            color = 'green';
        }
        
        return <Tag color={color}>{role.toUpperCase()}</Tag>;
      }
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => {
        const isActive = record.is_active;
        return (
          <Tag color={isActive ? 'green' : 'red'}>
            {isActive ? '已激活' : '已禁用'}
          </Tag>
        );
      }
    },
    {
      title: '注册时间',
      key: 'date_joined',
      render: (_, record) => (
        <span>{record.date_joined ? moment(record.date_joined).format('YYYY-MM-DD HH:mm') : '-'}</span>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        // 不允许对管理员执行某些操作
        const isAdmin = record.profile?.role === 'admin';
        
        return (
          <Space size="small">
            <Button
              type="link"
              icon={<LockOutlined />}
              onClick={() => showPasswordModal(record)}
              title="修改密码"
            >
              密码
            </Button>
            
            {!isAdmin && (
              <>
                <Switch
                  checked={record.is_active}
                  onChange={() => handleToggleActive(record)}
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                />
                
                <Popconfirm
                  title="确定要删除这个用户吗？"
                  onConfirm={() => handleDeleteUser(record)}
                  okText="是"
                  cancelText="否"
                >
                  <Button
                    type="link"
                    danger
                    icon={<DeleteOutlined />}
                    title="删除用户"
                  >
                    删除
                  </Button>
                </Popconfirm>
              </>
            )}
          </Space>
        );
      }
    }
  ];

  return (
    <div className="user-management">
      <Card title="用户管理" bordered={false}>
        {/* 搜索和筛选区域 */}
        <div style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={8}>
              <Input.Search
                placeholder="搜索用户名或邮箱"
                value={searchKeyword}
                onChange={e => setSearchKeyword(e.target.value)}
                onSearch={handleSearch}
                enterButton
              />
            </Col>
            <Col span={4}>
              <Select
                style={{ width: '100%' }}
                placeholder="状态筛选"
                value={statusFilter}
                onChange={handleStatusFilterChange}
              >
                <Option value="all">全部状态</Option>
                <Option value="active">已激活</Option>
                <Option value="inactive">已禁用</Option>
              </Select>
            </Col>
            <Col span={4}>
              <Select
                style={{ width: '100%' }}
                placeholder="角色筛选"
                value={roleFilter}
                onChange={handleRoleFilterChange}
              >
                <Option value="all">全部角色</Option>
                <Option value="admin">管理员</Option>
                <Option value="staff">员工</Option>
                <Option value="user">普通用户</Option>
              </Select>
            </Col>
          </Row>
        </div>
        
        {/* 用户列表表格 */}
        <Table
          columns={columns}
          dataSource={users}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
        
        {/* 分页控件 */}
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Pagination
            current={pagination.current}
            pageSize={pagination.pageSize}
            total={pagination.total}
            onChange={handlePageChange}
            showSizeChanger={false}
          />
        </div>
      </Card>
      
      {/* 修改密码对话框 */}
      <Modal
        title="修改用户密码"
        visible={passwordModalVisible}
        onOk={handleChangePassword}
        onCancel={() => setPasswordModalVisible(false)}
        okText="确定"
        cancelText="取消"
      >
        <Form layout="vertical">
          <Form.Item label="用户名">
            <Input value={selectedUser?.username} disabled />
          </Form.Item>
          <Form.Item label="新密码" required>
            <Input.Password
              value={newPassword}
              onChange={e => setNewPassword(e.target.value)}
              placeholder="请输入新密码"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement; 