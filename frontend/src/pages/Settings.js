import React, { useState } from 'react';
import { Tabs, Card } from 'antd';
import ChangePasswordForm from '../components/ChangePasswordForm';

const { TabPane } = Tabs;

const Settings = () => {
  return (
    <div className="settings-page">
      <Card title="个人设置" bordered={false}>
        <Tabs defaultActiveKey="password">
          <TabPane tab="修改密码" key="password">
            <ChangePasswordForm />
          </TabPane>
          <TabPane tab="个人资料" key="profile">
            <p>个人资料设置功能将在未来版本中推出</p>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default Settings; 