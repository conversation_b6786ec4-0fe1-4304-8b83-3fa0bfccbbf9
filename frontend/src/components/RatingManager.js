import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, message, Spin, Descriptions, Space, Rate } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { ratingAPI } from '../api/api';
import RatingForm from './RatingForm';

const { Title, Text } = Typography;

const RatingManager = ({ storeId }) => {
  const [loading, setLoading] = useState(true);
  const [rating, setRating] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (storeId) {
      fetchRatingData();
    }
  }, [storeId]);

  const fetchRatingData = async () => {
    setLoading(true);
    try {
      const response = await ratingAPI.getAll({ store: storeId });
      const ratingData = response.data.results || [];
      
      if (ratingData.length > 0) {
        setRating(ratingData[0]);
      } else {
        setRating(null);
      }
    } catch (error) {
      console.error('Error fetching rating data:', error);
      message.error('获取综合评分信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddRating = () => {
    setIsEditing(true);
  };

  const handleEditRating = () => {
    setIsEditing(true);
  };

  const handleRatingSaved = () => {
    setIsEditing(false);
    fetchRatingData();
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
        <p>加载综合评分信息...</p>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div>
        <Title level={4}>
          {rating ? '编辑综合评分' : '添加综合评分'}
        </Title>
        <RatingForm
          storeId={storeId}
          ratingId={rating?.id}
          onSuccess={handleRatingSaved}
        />
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button onClick={() => setIsEditing(false)}>取消</Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Title level={4}>综合评分与分析</Title>
        {rating ? (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEditRating}
          >
            编辑综合评分
          </Button>
        ) : (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddRating}
          >
            添加综合评分
          </Button>
        )}
      </div>
      
      {rating ? (
        <Card>
          <div style={{ marginBottom: 24 }}>
            <Title level={5}>各维度评分</Title>
            <Descriptions bordered>
              <Descriptions.Item label="服务质量评分" span={1}>
                {rating.service_quality_rating ? <Rate disabled allowHalf defaultValue={rating.service_quality_rating} /> : '未评分'}
              </Descriptions.Item>
              <Descriptions.Item label="产品多样性评分" span={1}>
                {rating.product_variety_rating ? <Rate disabled allowHalf defaultValue={rating.product_variety_rating} /> : '未评分'}
              </Descriptions.Item>
              <Descriptions.Item label="价格/性价比评分" span={1}>
                {rating.price_value_rating ? <Rate disabled allowHalf defaultValue={rating.price_value_rating} /> : '未评分'}
              </Descriptions.Item>
              <Descriptions.Item label="环境舒适度评分" span={1}>
                {rating.environment_rating ? <Rate disabled allowHalf defaultValue={rating.environment_rating} /> : '未评分'}
              </Descriptions.Item>
              <Descriptions.Item label="员工专业度评分" span={1}>
                {rating.staff_professionalism_rating ? <Rate disabled allowHalf defaultValue={rating.staff_professionalism_rating} /> : '未评分'}
              </Descriptions.Item>
              <Descriptions.Item label="综合评分" span={1}>
                {rating.overall_rating ? <Rate disabled allowHalf defaultValue={rating.overall_rating} /> : '未评分'}
              </Descriptions.Item>
            </Descriptions>
          </div>
          
          <div>
            <Title level={5}>竞争分析</Title>
            <Descriptions bordered>
              {rating.market_positioning && (
                <Descriptions.Item label="市场定位评估" span={3}>
                  {rating.market_positioning}
                </Descriptions.Item>
              )}
              
              {rating.competitive_advantages && (
                <Descriptions.Item label="竞争优势" span={3}>
                  {rating.competitive_advantages}
                </Descriptions.Item>
              )}
              
              {rating.competitive_disadvantages && (
                <Descriptions.Item label="竞争劣势" span={3}>
                  {rating.competitive_disadvantages}
                </Descriptions.Item>
              )}
              
              {rating.customer_loyalty_assessment && (
                <Descriptions.Item label="顾客忠诚度评估" span={3}>
                  {rating.customer_loyalty_assessment}
                </Descriptions.Item>
              )}
              
              <Descriptions.Item label="市场影响力评分">
                {rating.market_influence_score ? `${rating.market_influence_score} 分` : '未评分'}
              </Descriptions.Item>
              
              <Descriptions.Item label="未来发展潜力评分">
                {rating.future_potential_score ? `${rating.future_potential_score} 分` : '未评分'}
              </Descriptions.Item>
              
              {rating.threat_assessment && (
                <Descriptions.Item label="威胁程度评估" span={3}>
                  {rating.threat_assessment}
                </Descriptions.Item>
              )}
              
              {rating.improvement_suggestions && (
                <Descriptions.Item label="改进建议" span={3}>
                  {rating.improvement_suggestions}
                </Descriptions.Item>
              )}
              
              {rating.notes && (
                <Descriptions.Item label="其他备注" span={3}>
                  {rating.notes}
                </Descriptions.Item>
              )}
            </Descriptions>
          </div>
        </Card>
      ) : (
        <div style={{ textAlign: 'center', padding: '30px' }}>
          <Text type="secondary">暂无综合评分信息，请点击"添加综合评分"按钮添加</Text>
        </div>
      )}
    </div>
  );
};

export default RatingManager; 