import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Tag, Space, Button, message } from 'antd';
import { ReloadOutlined } from '@ant-design/icons';
import { 
  defaultMarkerConfig, 
  AMAP_ONLINE_ICONS, 
  LOCAL_SVG_ICONS,
  COMPETITION_COLORS,
  COMPETITION_LABELS 
} from '../config/markerConfig';

const IconTest = () => {
  const [iconLoadStatus, setIconLoadStatus] = useState({});

  useEffect(() => {
    testIconLoading();
  }, []);

  const testIconLoading = () => {
    const status = {};
    
    // 测试在线图标加载
    Object.entries(AMAP_ONLINE_ICONS).forEach(([key, url]) => {
      const img = new Image();
      img.onload = () => {
        setIconLoadStatus(prev => ({ ...prev, [key]: 'success' }));
      };
      img.onerror = () => {
        setIconLoadStatus(prev => ({ ...prev, [key]: 'error' }));
      };
      img.src = url;
      status[key] = 'loading';
    });
    
    setIconLoadStatus(status);
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'success': return 'green';
      case 'error': return 'red';
      case 'loading': return 'blue';
      default: return 'gray';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'success': return '加载成功';
      case 'error': return '加载失败';
      case 'loading': return '加载中...';
      default: return '未测试';
    }
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card 
        title="地图标记图标测试" 
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={testIconLoading}
          >
            重新测试
          </Button>
        }
      >
        <Row gutter={[16, 16]}>
          {Object.entries(COMPETITION_LABELS).map(([key, label]) => (
            <Col span={8} key={key}>
              <Card size="small" style={{ textAlign: 'center' }}>
                <Space direction="vertical" align="center">
                  <div style={{ fontSize: '16px', fontWeight: 'bold' }}>
                    {label}
                  </div>
                  
                  <div style={{ 
                    width: '40px', 
                    height: '40px', 
                    backgroundColor: COMPETITION_COLORS[key],
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    color: 'white',
                    fontWeight: 'bold'
                  }}>
                    {key.charAt(0).toUpperCase()}
                  </div>
                  
                  <div>
                    <img 
                      src={AMAP_ONLINE_ICONS[key]} 
                      alt={label}
                      style={{ width: '32px', height: '32px' }}
                      onError={(e) => {
                        e.target.style.display = 'none';
                        e.target.nextSibling.style.display = 'block';
                      }}
                    />
                    <div style={{ display: 'none', color: 'red' }}>
                      图标加载失败
                    </div>
                  </div>
                  
                  <Tag color={getStatusColor(iconLoadStatus[key])}>
                    {getStatusText(iconLoadStatus[key])}
                  </Tag>
                  
                  <div style={{ fontSize: '12px', color: '#666' }}>
                    {key}
                  </div>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
        
        <div style={{ marginTop: '24px' }}>
          <h3>图标URL列表</h3>
          <div style={{ backgroundColor: '#f5f5f5', padding: '16px', borderRadius: '4px' }}>
            {Object.entries(AMAP_ONLINE_ICONS).map(([key, url]) => (
              <div key={key} style={{ marginBottom: '8px' }}>
                <strong>{key}:</strong> 
                <a href={url} target="_blank" rel="noopener noreferrer" style={{ marginLeft: '8px' }}>
                  {url}
                </a>
                <Tag 
                  color={getStatusColor(iconLoadStatus[key])} 
                  style={{ marginLeft: '8px' }}
                >
                  {getStatusText(iconLoadStatus[key])}
                </Tag>
              </div>
            ))}
          </div>
        </div>
        
        <div style={{ marginTop: '16px' }}>
          <h3>修复说明</h3>
          <ul>
            <li>✅ 移除了本地绝对路径图标，改用高德地图在线图标</li>
            <li>✅ 创建了统一的图标配置管理系统</li>
            <li>✅ 支持多种竞争关系类型的图标显示</li>
            <li>✅ 提供了本地SVG图标的备选方案</li>
            <li>✅ 统一了所有地图组件的图标使用方式</li>
          </ul>
        </div>
      </Card>
    </div>
  );
};

export default IconTest;
