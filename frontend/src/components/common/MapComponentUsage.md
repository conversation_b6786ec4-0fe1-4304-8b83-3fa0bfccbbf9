# 地图组件使用指南

本文档描述了如何在不同场景中使用通用的高德地图组件（MapComponent），以及如何使用封装好的 AMapLocationPicker 组件。

## 基础配置

系统使用了全局的高德地图配置，位于 `/src/config/mapConfig.js`。这包括：

- API密钥
- 安全密钥
- API版本
- 默认插件列表
- 默认中心点和缩放级别

应用启动时会自动初始化安全配置。如果需要更新密钥，只需在这个文件中修改。

## 组件概述

### MapComponent

`MapComponent` 是一个功能全面的高德地图组件，提供了地图显示、地点搜索、地理位置选择等功能。

位置：`/src/components/common/MapComponent.js`

### AMapLocationPicker

`AMapLocationPicker` 是对 `MapComponent` 的封装，专门用于地址选择场景，包括地图显示和地址信息回调。

位置：`/src/components/AMapLocationPicker.js`

## 使用场景

### 1. 添加门店（新增场景）

在新增门店页面，使用 `AMapLocationPicker` 组件进行地址选择：

```jsx
<AMapLocationPicker 
  onLocationSelect={handleLocationSelect} 
  onAddressChange={handleAddressChange}
  key={`map-location-${activeTab}-${Date.now()}`} // 确保组件正确重新挂载
  initialLocation={{ 
    lng: form.getFieldValue('longitude') || 116.397428, 
    lat: form.getFieldValue('latitude') || 39.90923 
  }}
/>
```

回调函数实现：

```jsx
const handleLocationSelect = (location) => {
  form.setFieldsValue({
    longitude: location.lng,
    latitude: location.lat,
  });
};

const handleAddressChange = (addressInfo) => {
  form.setFieldsValue({
    address: addressInfo.formattedAddress,
    province: addressInfo.province,
    city: addressInfo.city,
    district: addressInfo.district,
  });
};
```

### 2. 编辑门店（编辑场景）

在编辑门店页面，同样使用 `AMapLocationPicker` 组件，但需传入初始位置信息：

```jsx
<AMapLocationPicker 
  onLocationSelect={handleLocationSelect} 
  onAddressChange={handleAddressChange}
  key={`map-edit-store-${Date.now()}`}
  initialLocation={{ 
    lng: storeData.longitude, 
    lat: storeData.latitude 
  }}
  initialAddress={{
    formattedAddress: storeData.address,
    province: storeData.province,
    city: storeData.city,
    district: storeData.district
  }}
/>
```

### 3. 查看门店（只读场景）

在查看门店页面，使用 `AMapLocationPicker` 的只读模式：

```jsx
<AMapLocationPicker 
  initialLocation={{ 
    lng: storeData.longitude, 
    lat: storeData.latitude 
  }}
  initialAddress={{
    formattedAddress: storeData.address,
    province: storeData.province,
    city: storeData.city,
    district: storeData.district
  }}
  readOnly={true}
/>
```

### 4. 直接使用 MapComponent（自定义场景）

如果需要更多的自定义功能，可直接使用 `MapComponent`：

```jsx
<MapComponent
  initialCenter={[116.397428, 39.90923]}
  initialZoom={15}
  initialMarkerPosition={[116.397428, 39.90923]}
  onMapClick={(location, addressInfo) => {
    console.log('Map clicked:', location, addressInfo);
  }}
  onMarkerDragEnd={(location, addressInfo) => {
    console.log('Marker dragged:', location, addressInfo);
  }}
  onAddressSelect={(poiData, addressInfo) => {
    console.log('Address selected:', poiData, addressInfo);
  }}
  addressInputId="your-input-element-id" // 可选，用于关联地址输入框
  isEditable={true}
  style={{ height: '400px', width: '100%' }}
  searchPlaceholder="请输入关键词搜索"
  showToolbar={true}
  showSearch={true}
  showGeolocation={true}
/>
```

## 实现可靠性建议

1. **总是使用 key 属性**：为了确保地图组件在同一页面上正确重新挂载，总是提供一个唯一的 key 属性。

2. **错误处理**：地图组件内部已实现错误处理和自动重试机制，但在集成时也应处理可能的错误情况。

3. **地址格式化**：从地图获取的地址信息（如省市区）已经是结构化的，可直接用于表单填充。

4. **性能考虑**：当不需要显示地图时（如切换到其他标签页），可考虑使用条件渲染来卸载地图组件，以节省资源。

5. **响应式设计**：地图组件支持自定义样式，可结合 CSS 媒体查询实现响应式设计。

## 常见问题排查

1. **地图无法加载**：检查网络连接和API密钥是否正确。

2. **标记点不显示**：确保传入了有效的 initialMarkerPosition。

3. **地址选择不工作**：检查 addressInputId 是否正确关联到输入框。

4. **地图容器未找到**：确保组件正确挂载，并且有适当的延迟初始化。

5. **地图在Tab切换后消失**：确保提供了正确的 key 属性，以强制组件在Tab切换时重新挂载。 