import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, message, Spin, Descriptions, Space } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { environmentAPI } from '../api/api';
import EnvironmentForm from './EnvironmentForm';

const { Title, Text } = Typography;

const EnvironmentManager = ({ storeId }) => {
  const [loading, setLoading] = useState(true);
  const [environment, setEnvironment] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (storeId) {
      fetchEnvironmentData();
    }
  }, [storeId]);

  const fetchEnvironmentData = async () => {
    setLoading(true);
    try {
      const response = await environmentAPI.getAll({ store: storeId });
      const environmentData = response.data.results || [];
      
      if (environmentData.length > 0) {
        setEnvironment(environmentData[0]);
      } else {
        setEnvironment(null);
      }
    } catch (error) {
      console.error('Error fetching environment data:', error);
      message.error('获取门店环境与设施信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddEnvironment = () => {
    setIsEditing(true);
  };

  const handleEditEnvironment = () => {
    setIsEditing(true);
  };

  const handleEnvironmentSaved = () => {
    setIsEditing(false);
    fetchEnvironmentData();
  };

  const renderSmellText = (smell) => {
    const smellMap = {
      fresh: '清新无异味',
      light: '轻微宠物味',
      moderate: '较重宠物味',
      unpleasant: '难闻',
    };
    return smellMap[smell] || smell;
  };

  const renderLightingText = (lighting) => {
    const lightingMap = {
      very_bright: '非常明亮',
      bright: '明亮',
      average: '一般',
      dim: '偏暗',
    };
    return lightingMap[lighting] || lighting;
  };

  const renderVentilationText = (ventilation) => {
    const ventilationMap = {
      good: '良好',
      average: '一般',
      poor: '较差',
    };
    return ventilationMap[ventilation] || ventilation;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
        <p>加载门店环境与设施信息...</p>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div>
        <Title level={4}>
          {environment ? '编辑门店环境与设施' : '添加门店环境与设施'}
        </Title>
        <EnvironmentForm
          storeId={storeId}
          environmentId={environment?.id}
          onSuccess={handleEnvironmentSaved}
        />
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button onClick={() => setIsEditing(false)}>取消</Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Title level={4}>门店环境与设施</Title>
        {environment ? (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEditEnvironment}
          >
            编辑环境信息
          </Button>
        ) : (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddEnvironment}
          >
            添加环境信息
          </Button>
        )}
      </div>
      
      {environment ? (
        <Card>
          <Descriptions title="环境与设施" bordered>
            <Descriptions.Item label="门店清洁度">
              {environment.cleanliness ? `${environment.cleanliness} 分` : '未评分'}
            </Descriptions.Item>
            <Descriptions.Item label="店内气味">
              {environment.smell ? renderSmellText(environment.smell) : '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="装修风格">
              {environment.decoration_style || '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="空间布局合理性">
              {environment.layout_rationality ? `${environment.layout_rationality} 分` : '未评分'}
            </Descriptions.Item>
            <Descriptions.Item label="采光情况">
              {environment.lighting ? renderLightingText(environment.lighting) : '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="通风情况">
              {environment.ventilation ? renderVentilationText(environment.ventilation) : '未知'}
            </Descriptions.Item>
            
            {environment.beauty_equipment && (
              <Descriptions.Item label="美容区设备情况" span={3}>
                {environment.beauty_equipment}
              </Descriptions.Item>
            )}
            
            {environment.retail_display && (
              <Descriptions.Item label="零售区货架与陈列" span={3}>
                {environment.retail_display}
              </Descriptions.Item>
            )}
            
            {environment.customer_facilities && (
              <Descriptions.Item label="顾客便利设施" span={3}>
                {environment.customer_facilities}
              </Descriptions.Item>
            )}
            
            {environment.waiting_area && (
              <Descriptions.Item label="等候区设置" span={3}>
                {environment.waiting_area}
              </Descriptions.Item>
            )}
            
            {environment.store_atmosphere && (
              <Descriptions.Item label="店铺整体氛围" span={3}>
                {environment.store_atmosphere}
              </Descriptions.Item>
            )}
            
            {environment.notes && (
              <Descriptions.Item label="其他备注" span={3}>
                {environment.notes}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      ) : (
        <div style={{ textAlign: 'center', padding: '30px' }}>
          <Text type="secondary">暂无门店环境与设施信息，请点击"添加环境信息"按钮添加</Text>
        </div>
      )}
    </div>
  );
};

export default EnvironmentManager; 