import React, { useState } from 'react';
import { 
  Card, Button, Table, Tag, Space, message, Modal, 
  Descriptions, Progress, Statistic, Row, Col, Alert,
  Switch, Tooltip, Divider
} from 'antd';
import { 
  CloudDownloadOutlined, InfoCircleOutlined, 
  CheckCircleOutlined, ExclamationCircleOutlined,
  SyncOutlined, DatabaseOutlined
} from '@ant-design/icons';
import { storeAPI } from '../api/api';

const AmapDataEnricher = ({ poiData, onEnrichmentComplete }) => {
  const [loading, setLoading] = useState(false);
  const [enrichmentResult, setEnrichmentResult] = useState(null);
  const [showDetails, setShowDetails] = useState(false);
  const [createIfNotExists, setCreateIfNotExists] = useState(true);

  // 高德POI数据字段映射
  const poiFieldMapping = {
    id: 'POI ID',
    name: '门店名称',
    address: '详细地址',
    location: '经纬度坐标',
    tel: '电话号码',
    typecode: 'POI类型编码',
    type: 'POI类型',
    distance: '距离(米)',
    'biz_ext.open_time': '营业时间',
    'biz_ext.rating': '评分',
    'biz_ext.cost': '消费水平',
    'biz_ext.tag': '特色标签'
  };

  // 可补充的门店字段
  const enrichableFields = {
    name: '门店名称',
    brand_name: '品牌名称',
    address: '详细地址',
    longitude: '经度',
    latitude: '纬度',
    business_district: '商圈',
    phone: '联系电话',
    store_type: '门店类型',
    main_business: '主营业务',
    competition_relation: '竞争关系',
    business_hours_weekday: '工作日营业时间',
    business_hours_weekend: '周末营业时间',
    notes: '备注信息'
  };

  const handleEnrichData = async () => {
    if (!poiData) {
      message.error('没有可用的POI数据');
      return;
    }

    setLoading(true);
    try {
      const response = await storeAPI.enrichFromAmap({
        poi_data: poiData,
        create_if_not_exists: createIfNotExists
      });

      setEnrichmentResult(response.data);
      
      if (response.data.success) {
        const { action, enriched_fields } = response.data;
        
        if (action === 'created') {
          message.success(`成功创建新门店，补充了 ${enriched_fields.length} 个字段`);
        } else if (action === 'updated') {
          message.success(`成功更新门店信息，补充了 ${enriched_fields.length} 个字段`);
        } else {
          message.info('门店已存在，无需更新');
        }

        if (onEnrichmentComplete) {
          onEnrichmentComplete(response.data);
        }
      } else {
        message.error(response.data.error || '数据补充失败');
      }
    } catch (error) {
      console.error('数据补充失败:', error);
      message.error('数据补充失败');
    } finally {
      setLoading(false);
    }
  };

  const getFieldValue = (obj, path) => {
    return path.split('.').reduce((current, key) => current?.[key], obj);
  };

  const renderPoiInfo = () => {
    if (!poiData) return null;

    const items = Object.entries(poiFieldMapping).map(([key, label]) => {
      const value = getFieldValue(poiData, key);
      return {
        key,
        label,
        children: value ? String(value) : '-'
      };
    });

    return (
      <Descriptions 
        title="高德POI数据" 
        size="small" 
        column={2}
        items={items}
      />
    );
  };

  const renderEnrichmentResult = () => {
    if (!enrichmentResult) return null;

    const { action, enriched_fields, enrichment_stats } = enrichmentResult;

    const actionConfig = {
      created: { color: 'green', icon: <CheckCircleOutlined />, text: '新建门店' },
      updated: { color: 'blue', icon: <SyncOutlined />, text: '更新门店' },
      found: { color: 'orange', icon: <InfoCircleOutlined />, text: '门店已存在' }
    };

    const config = actionConfig[action] || actionConfig.found;

    return (
      <Card title="数据补充结果" style={{ marginTop: 16 }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <Statistic
              title="处理结果"
              value={config.text}
              prefix={config.icon}
              valueStyle={{ color: config.color }}
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="补充字段数"
              value={enriched_fields?.length || 0}
              suffix="个"
            />
          </Col>
          <Col span={8}>
            <Statistic
              title="成功率"
              value={enrichment_stats?.success_rate || 100}
              suffix="%"
            />
          </Col>
        </Row>

        {enriched_fields && enriched_fields.length > 0 && (
          <div style={{ marginTop: 16 }}>
            <h4>补充的字段:</h4>
            <Space wrap>
              {enriched_fields.map(field => (
                <Tag key={field} color="blue">
                  {enrichableFields[field] || field}
                </Tag>
              ))}
            </Space>
          </div>
        )}
      </Card>
    );
  };

  return (
    <div>
      <Card 
        title={
          <Space>
            <DatabaseOutlined />
            高德数据自动补充
          </Space>
        }
        extra={
          <Space>
            <Tooltip title="如果门店不存在，是否自动创建新门店">
              <Switch
                checked={createIfNotExists}
                onChange={setCreateIfNotExists}
                checkedChildren="自动创建"
                unCheckedChildren="仅更新"
              />
            </Tooltip>
            <Button
              type="primary"
              icon={<CloudDownloadOutlined />}
              loading={loading}
              onClick={handleEnrichData}
              disabled={!poiData}
            >
              补充数据
            </Button>
            <Button
              icon={<InfoCircleOutlined />}
              onClick={() => setShowDetails(true)}
              disabled={!poiData}
            >
              查看详情
            </Button>
          </Space>
        }
      >
        <Alert
          message="数据补充说明"
          description="系统将从高德POI数据中提取门店名称、地址、电话、营业时间等信息，自动补充到门店数据库中。只会更新空字段，不会覆盖已有数据。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        {poiData && (
          <div>
            <h4>待补充的POI数据:</h4>
            <Descriptions size="small" column={1}>
              <Descriptions.Item label="门店名称">
                {poiData.name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="地址">
                {poiData.address || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="电话">
                {poiData.tel || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="类型">
                {poiData.type || '-'}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}

        {!poiData && (
          <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
            <DatabaseOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>请先选择要补充的POI数据</div>
          </div>
        )}

        {renderEnrichmentResult()}
      </Card>

      <Modal
        title="POI数据详情"
        open={showDetails}
        onCancel={() => setShowDetails(false)}
        footer={null}
        width={800}
      >
        {renderPoiInfo()}
        
        <Divider />
        
        <h4>可补充的门店字段:</h4>
        <Row gutter={[8, 8]}>
          {Object.entries(enrichableFields).map(([key, label]) => (
            <Col key={key} span={8}>
              <Tag color="blue">{label}</Tag>
            </Col>
          ))}
        </Row>
      </Modal>
    </div>
  );
};

export default AmapDataEnricher;
