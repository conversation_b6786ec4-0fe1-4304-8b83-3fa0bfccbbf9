import React, { useState, useEffect } from 'react';
import { 
  Table, Button, Input, Select, Row, Col, Card, Space, Divider, message, 
  Image, Avatar, Collapse, Form, InputNumber, Slider, Tag, DatePicker 
} from 'antd';
import { 
  SearchOutlined, PlusOutlined, EditOutlined, EyeOutlined, DeleteOutlined, 
  ShopOutlined, FilterOutlined, DownOutlined, UpOutlined, SyncOutlined 
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { storeAPI, brandAPI } from '../api/api';

const { Option } = Select;
const { Panel } = Collapse;
const { RangePicker } = DatePicker;

const StoreList = () => {
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [brands, setBrands] = useState([]);
  const [advancedFilterVisible, setAdvancedFilterVisible] = useState(false);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 15,
    total: 0,
    pageSizeOptions: [15, 25, 50],
    showSizeChanger: true,
  });
  const [filters, setFilters] = useState({
    search: '',
    store_type: '',
    competition_relation: '',
    business_district: '',
    brand_name: '',
    store_area_min: null,
    store_area_max: null,
    staff_min: null,
    staff_max: null,
    opening_year_min: null,
    opening_year_max: null,
    rating_level: []
  });
  const [totalRecords, setTotalRecords] = useState(0);

  useEffect(() => {
    fetchStores();
    fetchBrands();
    
    // 如果没有任何筛选条件，则更新总记录数
    if (getActiveFilterCount() === 0 && !filters.search && !filters.store_type && !filters.competition_relation) {
      setTotalRecords(pagination.total);
    }
  }, [pagination.current, filters]);

  const fetchBrands = async () => {
    try {
      // 先从门店数据中提取品牌名
      const storeResponse = await storeAPI.getAll({
        page_size: 100, // 获取足够多的门店数据来提取品牌名
      });
      
      // 从门店数据中提取唯一的品牌名
      const storeBrands = storeResponse.data.results
        .filter(store => store.brand_name)
        .map(store => store.brand_name);
      
      // 尝试从品牌API获取更多品牌数据
      let brandApiData = [];
      try {
        const brandResponse = await brandAPI.getAll();
        brandApiData = brandResponse.data.results.map(brand => brand.brand_name);
      } catch (err) {
        console.log('品牌API数据获取失败，将仅使用门店数据中的品牌名', err);
      }
      
      // 合并并去重
      const allBrands = [...storeBrands, ...brandApiData];
      const uniqueBrands = Array.from(new Set(allBrands)).filter(Boolean).sort();
      
      setBrands(uniqueBrands);
    } catch (error) {
      console.error('Error fetching brands:', error);
      message.error('品牌数据加载失败');
    }
  };

  const fetchStores = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: filters.search,
        store_type: filters.store_type,
        competition_relation: filters.competition_relation,
        business_district: filters.business_district,
        brand_name: filters.brand_name
      };
      
      // 添加数值范围过滤
      if (filters.store_area_min !== null) params.store_area_min = filters.store_area_min;
      if (filters.store_area_max !== null) params.store_area_max = filters.store_area_max;
      if (filters.staff_min !== null) params.staff_min = filters.staff_min;
      if (filters.staff_max !== null) params.staff_max = filters.staff_max;
      if (filters.opening_year_min !== null) params.opening_year_min = filters.opening_year_min;
      if (filters.opening_year_max !== null) params.opening_year_max = filters.opening_year_max;
      if (filters.rating_level && filters.rating_level.length > 0) params.rating_level = filters.rating_level.join(',');
      
      const response = await storeAPI.getAll(params);
      setStores(response.data.results);
      setPagination({
        ...pagination,
        total: response.data.count,
        pageSizeOptions: response.data.page_size_options || [15, 25, 50]
      });
      
      // 如果没有筛选条件，更新总记录数
      const hasFilters = getActiveFilterCount() > 0 || filters.search || filters.store_type || filters.competition_relation;
      if (!hasFilters) {
        setTotalRecords(response.data.count);
      }
    } catch (error) {
      console.error('Error fetching stores:', error);
      message.error('获取门店列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  const handleSearch = (value) => {
    setFilters({ ...filters, search: value });
    setPagination({ ...pagination, current: 1 });
  };

  const handleFilterChange = (field, value) => {
    setFilters({ ...filters, [field]: value });
    setPagination({ ...pagination, current: 1 });
  };

  const handleRangeChange = (field, values) => {
    const [min, max] = values;
    setFilters({ 
      ...filters, 
      [`${field}_min`]: min, 
      [`${field}_max`]: max 
    });
    setPagination({ ...pagination, current: 1 });
  };

  const handleAdvancedSearch = (values) => {
    // 处理品牌名，如果是数组则取第一个值
    let brandName = values.brand_name;
    if (Array.isArray(brandName) && brandName.length > 0) {
      brandName = brandName[0];
    } else if (Array.isArray(brandName) && brandName.length === 0) {
      brandName = '';
    }
    
    // 如果有品牌名，保存到最近使用的品牌列表
    if (brandName) {
      saveRecentBrand(brandName);
    }
    
    const newFilters = {
      ...filters,
      brand_name: brandName,
      store_area_min: values.store_area?.[0],
      store_area_max: values.store_area?.[1],
      staff_min: values.staff?.[0],
      staff_max: values.staff?.[1],
      opening_year_min: values.opening_year?.[0],
      opening_year_max: values.opening_year?.[1],
      rating_level: values.rating_level
    };
    setFilters(newFilters);
    setPagination({ ...pagination, current: 1 });
  };

  const resetFilters = () => {
    form.resetFields();
    setFilters({
      search: '',
      store_type: '',
      competition_relation: '',
      business_district: '',
      brand_name: '',
      store_area_min: null,
      store_area_max: null,
      staff_min: null,
      staff_max: null,
      opening_year_min: null,
      opening_year_max: null,
      rating_level: []
    });
    setPagination({ ...pagination, current: 1 });
  };

  const handleDelete = async (id) => {
    try {
      await storeAPI.delete(id);
      message.success('门店删除成功');
      fetchStores();
    } catch (error) {
      console.error('Error deleting store:', error);
      message.error('门店删除失败');
    }
  };

  // 保存最近使用的品牌名到localStorage
  const saveRecentBrand = (brandName) => {
    if (!brandName) return;
    
    try {
      // 获取现有的最近使用品牌
      const recentBrandsJSON = localStorage.getItem('recentBrands') || '[]';
      let recentBrands = JSON.parse(recentBrandsJSON);
      
      // 确保是数组
      if (!Array.isArray(recentBrands)) recentBrands = [];
      
      // 如果已存在，从列表中移除（稍后会添加到列表开头）
      recentBrands = recentBrands.filter(b => b !== brandName);
      
      // 添加到列表开头
      recentBrands.unshift(brandName);
      
      // 保留最近的10个品牌
      if (recentBrands.length > 10) {
        recentBrands = recentBrands.slice(0, 10);
      }
      
      // 保存回localStorage
      localStorage.setItem('recentBrands', JSON.stringify(recentBrands));
    } catch (error) {
      console.error('保存最近使用品牌失败', error);
    }
  };
  
  // 获取最近使用的品牌列表
  const getRecentBrands = () => {
    try {
      const recentBrandsJSON = localStorage.getItem('recentBrands') || '[]';
      const recentBrands = JSON.parse(recentBrandsJSON);
      return Array.isArray(recentBrands) ? recentBrands : [];
    } catch (error) {
      console.error('获取最近使用品牌失败', error);
      return [];
    }
  };

  // 组合品牌列表 = 最近使用 + 系统提供
  const getCombinedBrandsList = () => {
    const recentBrands = getRecentBrands();
    // 过滤掉已经在recent中的品牌，避免重复
    const systemBrands = brands.filter(brand => !recentBrands.includes(brand));
    return [...recentBrands, ...systemBrands];
  };

  const columns = [
    {
      title: '图片',
      dataIndex: 'main_image',
      key: 'main_image',
      width: 80,
      render: (main_image, record) => (
        main_image ? (
          <Avatar 
            src={<Image src={main_image} style={{ width: 50 }} preview={false} />} 
            size={50} 
            shape="square"
            onClick={() => navigate(`/view-store/${record.id}`)}
            style={{ cursor: 'pointer' }}
          />
        ) : (
          <Avatar 
            icon={<ShopOutlined />} 
            size={50} 
            shape="square" 
            style={{ backgroundColor: '#87d068', cursor: 'pointer' }} 
            onClick={() => navigate(`/view-store/${record.id}`)}
          />
        )
      ),
    },
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
      sorter: true,
    },
    {
      title: '品牌名',
      dataIndex: 'brand_name',
      key: 'brand_name',
    },
    {
      title: '门店类型',
      dataIndex: 'store_type',
      key: 'store_type',
      render: (storeType) => {
        const typeMap = {
          direct_chain: '直营连锁',
          franchise_chain: '加盟连锁',
          single_store: '单体店',
          pet_hospital: '宠物医院附带',
          retail_only: '纯零售店',
        };
        return typeMap[storeType] || storeType;
      },
    },
    {
      title: '竞争关系',
      dataIndex: 'competition_relation',
      key: 'competition_relation',
      render: (relation) => {
        const relationMap = {
          direct_competitor: '直接竞争对手',
          indirect_hospital: '间接竞争对手-宠物医院',
          indirect_retail: '间接竞争对手-纯零售',
        };
        return relationMap[relation] || relation;
      },
    },
    {
      title: '商圈',
      dataIndex: 'business_district',
      key: 'business_district',
    },
    {
      title: '开业年份',
      dataIndex: 'opening_year',
      key: 'opening_year',
      sorter: true,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            icon={<EyeOutlined />}
            size="small"
            onClick={() => navigate(`/view-store/${record.id}`)}
          />
          <Button
            type="default"
            icon={<EditOutlined />}
            size="small"
            onClick={() => navigate(`/edit-store/${record.id}`)}
          />
          <Button
            type="default"
            danger
            icon={<DeleteOutlined />}
            size="small"
            onClick={() => handleDelete(record.id)}
          />
        </Space>
      ),
    },
  ];

  // 计算当前有效筛选条件数量
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.brand_name) count++;
    if (filters.store_area_min !== null || filters.store_area_max !== null) count++;
    if (filters.staff_min !== null || filters.staff_max !== null) count++;
    if (filters.opening_year_min !== null || filters.opening_year_max !== null) count++;
    if (filters.rating_level && filters.rating_level.length > 0) count++;
    return count;
  };

  // 获取所有记录总数（无筛选条件）
  const fetchTotalRecords = async () => {
    try {
      const response = await storeAPI.getAll({
        page: 1,
        page_size: 1 // 只需要获取总数，所以只请求一条记录
      });
      setTotalRecords(response.data.count);
    } catch (error) {
      console.error('Error fetching total records count:', error);
    }
  };

  // 在组件挂载时获取总记录数
  useEffect(() => {
    fetchTotalRecords();
  }, []);

  return (
    <div>
      <Card>
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={6}>
            <Input
              placeholder="搜索门店名称、品牌名、地址关键词"
              prefix={<SearchOutlined />}
              allowClear
              value={filters.search}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </Col>
          <Col xs={24} md={12} lg={12}>
            <Space>
              <Select
                placeholder="门店类型"
                style={{ width: 160 }}
                allowClear
                value={filters.store_type}
                onChange={(value) => handleFilterChange('store_type', value)}
              >
                <Option value="direct_chain">直营连锁</Option>
                <Option value="franchise_chain">加盟连锁</Option>
                <Option value="single_store">单体店</Option>
                <Option value="pet_hospital">宠物医院附带</Option>
                <Option value="retail_only">纯零售店</Option>
              </Select>
              <Select
                placeholder="竞争关系"
                style={{ width: 200 }}
                allowClear
                value={filters.competition_relation}
                onChange={(value) => handleFilterChange('competition_relation', value)}
              >
                <Option value="direct_competitor">直接竞争对手</Option>
                <Option value="indirect_hospital">间接竞争对手-宠物医院</Option>
                <Option value="indirect_retail">间接竞争对手-纯零售</Option>
              </Select>
              <Button
                type={advancedFilterVisible ? "primary" : "default"}
                icon={<FilterOutlined />}
                onClick={() => setAdvancedFilterVisible(!advancedFilterVisible)}
              >
                高级筛选
                {getActiveFilterCount() > 0 && (
                  <Tag color="blue" style={{ marginLeft: 5 }}>
                    {getActiveFilterCount()}
                  </Tag>
                )}
                {advancedFilterVisible ? <UpOutlined /> : <DownOutlined />}
              </Button>
            </Space>
          </Col>
          <Col xs={24} lg={6} style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={resetFilters} icon={<SyncOutlined />}>重置</Button>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/add-store')}
              >
                新增门店
              </Button>
            </Space>
          </Col>
        </Row>
        
        {advancedFilterVisible && (
          <div style={{ marginTop: 16 }}>
            <Card>
              <Form 
                form={form}
                layout="vertical"
                onFinish={handleAdvancedSearch}
                initialValues={{
                  brand_name: filters.brand_name,
                  store_area: filters.store_area_min !== null || filters.store_area_max !== null ? 
                    [filters.store_area_min, filters.store_area_max] : undefined,
                  staff: filters.staff_min !== null || filters.staff_max !== null ? 
                    [filters.staff_min, filters.staff_max] : undefined,
                  opening_year: filters.opening_year_min !== null || filters.opening_year_max !== null ? 
                    [filters.opening_year_min, filters.opening_year_max] : undefined,
                  rating_level: filters.rating_level
                }}
              >
                <Row gutter={24}>
                  <Col span={8}>
                    <Form.Item name="brand_name" label="品牌名">
                      <Select
                        placeholder="选择或直接输入品牌名"
                        allowClear
                        showSearch
                        optionFilterProp="children"
                        filterOption={(input, option) =>
                          option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                        }
                        mode="tags"
                        maxTagCount={1}
                        onChange={(value) => {
                          // 如果是数组（tags模式返回数组），取第一个元素
                          if (Array.isArray(value) && value.length > 0) {
                            form.setFieldsValue({ brand_name: value[0] });
                          }
                        }}
                        style={{ width: '100%' }}
                      >
                        {getCombinedBrandsList().map((brand, index, arr) => {
                          const recentBrands = getRecentBrands();
                          const isRecentBrand = index < recentBrands.length;
                          
                          // 如果是最后一个最近使用的品牌，且还有系统品牌，则添加分隔符
                          if (index === recentBrands.length - 1 && recentBrands.length > 0 && brands.length > 0) {
                            return [
                              <Option key={brand} value={brand}>
                                {isRecentBrand && <Tag color="blue" style={{ marginRight: 5 }}>最近</Tag>}
                                {brand}
                              </Option>,
                              <Option key="recent-divider" disabled style={{ borderTop: '1px solid #ccc', height: 0, margin: '5px 0' }}>
                                ──────────
                              </Option>
                            ];
                          }
                          
                          return (
                            <Option key={brand} value={brand}>
                              {isRecentBrand && <Tag color="blue" style={{ marginRight: 5 }}>最近</Tag>}
                              {brand}
                            </Option>
                          );
                        })}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="store_area" label="门店面积 (平米)">
                      <Input.Group compact>
                        <InputNumber
                          style={{ width: '45%' }}
                          placeholder="最小"
                          min={0}
                          onChange={(value) => {
                            const maxValue = form.getFieldValue(['store_area', 1]);
                            form.setFieldsValue({ store_area: [value, maxValue] });
                          }}
                        />
                        <Input
                          style={{ width: '10%', textAlign: 'center', pointerEvents: 'none' }}
                          placeholder="~"
                          disabled
                        />
                        <InputNumber
                          style={{ width: '45%' }}
                          placeholder="最大"
                          min={0}
                          onChange={(value) => {
                            const minValue = form.getFieldValue(['store_area', 0]);
                            form.setFieldsValue({ store_area: [minValue, value] });
                          }}
                        />
                      </Input.Group>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="staff" label="人员数量 (人)">
                      <Input.Group compact>
                        <InputNumber
                          style={{ width: '45%' }}
                          placeholder="最小"
                          min={0}
                          onChange={(value) => {
                            const maxValue = form.getFieldValue(['staff', 1]);
                            form.setFieldsValue({ staff: [value, maxValue] });
                          }}
                        />
                        <Input
                          style={{ width: '10%', textAlign: 'center', pointerEvents: 'none' }}
                          placeholder="~"
                          disabled
                        />
                        <InputNumber
                          style={{ width: '45%' }}
                          placeholder="最大"
                          min={0}
                          onChange={(value) => {
                            const minValue = form.getFieldValue(['staff', 0]);
                            form.setFieldsValue({ staff: [minValue, value] });
                          }}
                        />
                      </Input.Group>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={8}>
                    <Form.Item name="opening_year" label="开业年份">
                      <Input.Group compact>
                        <InputNumber
                          style={{ width: '45%' }}
                          placeholder="最小"
                          min={1990}
                          max={new Date().getFullYear()}
                          onChange={(value) => {
                            const maxValue = form.getFieldValue(['opening_year', 1]);
                            form.setFieldsValue({ opening_year: [value, maxValue] });
                          }}
                        />
                        <Input
                          style={{ width: '10%', textAlign: 'center', pointerEvents: 'none' }}
                          placeholder="~"
                          disabled
                        />
                        <InputNumber
                          style={{ width: '45%' }}
                          placeholder="最大"
                          min={1990}
                          max={new Date().getFullYear()}
                          onChange={(value) => {
                            const minValue = form.getFieldValue(['opening_year', 0]);
                            form.setFieldsValue({ opening_year: [minValue, value] });
                          }}
                        />
                      </Input.Group>
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item name="rating_level" label="综合评级">
                      <Select
                        placeholder="选择评级"
                        mode="multiple"
                        allowClear
                      >
                        <Option value="S">S级</Option>
                        <Option value="A">A级</Option>
                        <Option value="B">B级</Option>
                        <Option value="C">C级</Option>
                        <Option value="D">D级</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={8} style={{ textAlign: 'right', marginTop: 30 }}>
                    <Button type="primary" htmlType="submit">
                      应用筛选
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>
          </div>
        )}
      </Card>
      
      <Divider style={{ margin: '16px 0' }} />

      <Card>
        <Table
          rowKey="id"
          loading={loading}
          dataSource={stores}
          columns={columns}
          pagination={pagination}
          onChange={handleTableChange}
        />
        <Divider style={{ margin: '16px 0 8px 0' }} />
        <div style={{ textAlign: 'right', color: '#666', fontSize: '14px', padding: '0 8px 8px 0' }}>
          {(getActiveFilterCount() > 0 || filters.search || filters.store_type || filters.competition_relation) ? (
            <span>
              筛选结果：<strong style={{ color: '#1890ff', fontSize: '15px' }}>{pagination.total}</strong> 条记录
              {pagination.total > 0 && (
                <span>（每页 {pagination.pageSize} 条，共 {Math.ceil(pagination.total / pagination.pageSize)} 页）</span>
              )}
              <span style={{ marginLeft: 10, borderLeft: '1px solid #ddd', paddingLeft: 10 }}>总记录数: <strong>{totalRecords}</strong> 条</span>
            </span>
          ) : (
            <span>
              总计：<strong style={{ fontSize: '15px' }}>{pagination.total}</strong> 条记录
              {pagination.total > 0 && (
                <span>（每页 {pagination.pageSize} 条，共 {Math.ceil(pagination.total / pagination.pageSize)} 页）</span>
              )}
            </span>
          )}
        </div>
      </Card>
    </div>
  );
};

export default StoreList; 