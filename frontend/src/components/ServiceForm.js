import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Switch, Space, Card, Divider, message, Checkbox, Row, Col } from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';
import { serviceAPI } from '../api/api';

const { TextArea } = Input;
const { Option } = Select;

const ServiceForm = ({ storeId, serviceId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [appointmentMethods, setAppointmentMethods] = useState([]);

  const appointmentOptions = [
    { label: '电话预约', value: 'phone' },
    { label: '微信预约', value: 'wechat' },
    { label: '小程序预约', value: 'miniapp' },
    { label: '线上平台预约', value: 'platform' },
    { label: '现场预约', value: 'walk_in' },
  ];

  useEffect(() => {
    if (serviceId) {
      setIsEdit(true);
      fetchServiceData();
    } else {
      form.setFieldsValue({ store: storeId });
    }
  }, [storeId, serviceId]);

  const fetchServiceData = async () => {
    setLoading(true);
    try {
      const response = await serviceAPI.getById(serviceId);
      const serviceData = response.data;
      
      // Parse appointment methods if stored as string
      let methods = [];
      if (serviceData.appointment_methods) {
        try {
          methods = JSON.parse(serviceData.appointment_methods);
        } catch (e) {
          methods = serviceData.appointment_methods.split(',');
        }
      }
      setAppointmentMethods(methods);
      
      form.setFieldsValue({
        ...serviceData,
        appointment_methods: methods
      });
    } catch (error) {
      console.error('Error fetching service data:', error);
      message.error('获取服务信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAppointmentMethodsChange = (checkedValues) => {
    setAppointmentMethods(checkedValues);
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      // Convert appointment methods to string for storage
      const formData = {
        ...values,
        appointment_methods: JSON.stringify(values.appointment_methods || [])
      };
      
      if (isEdit) {
        await serviceAPI.update(serviceId, formData);
        message.success('服务项目更新成功');
      } else {
        await serviceAPI.create(formData);
        message.success('服务项目添加成功');
        form.resetFields();
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving service:', error);
      message.error(isEdit ? '服务项目更新失败' : '服务项目添加失败');
    } finally {
      setLoading(false);
    }
  };

  const serviceCategoryOptions = [
    { label: '洗护', value: '洗护' },
    { label: '美容', value: '美容' },
    { label: '寄养', value: '寄养' },
    { label: '训练', value: '训练' },
    { label: '医疗', value: '医疗' },
    { label: '其他', value: '其他' },
  ];

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          has_cat_area: false,
        }}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="service_category"
              label="服务大类"
              rules={[{ required: true, message: '请选择服务大类' }]}
            >
              <Select placeholder="请选择服务大类">
                {serviceCategoryOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="service_name"
              label="服务名称"
              rules={[{ required: true, message: '请输入服务名称' }]}
            >
              <Input placeholder="请输入服务名称" />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="service_description"
          label="服务内容描述"
        >
          <TextArea
            placeholder="请描述服务的具体内容、流程等"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="price_range"
              label="价格/价格范围"
            >
              <Input placeholder="例如：100-200元" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="member_price"
              label="会员价/套餐价"
            >
              <Input placeholder="例如：80-160元" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="product_brand"
              label="使用产品品牌"
            >
              <Input placeholder="使用的产品品牌" />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="appointment_methods"
          label="预约方式"
        >
          <Checkbox.Group 
            options={appointmentOptions} 
            value={appointmentMethods}
            onChange={handleAppointmentMethodsChange}
          />
        </Form.Item>
        
        <Form.Item
          name="waiting_area_facilities"
          label="等待区设施描述"
        >
          <TextArea
            placeholder="描述等待区的设施、环境等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="has_cat_area"
          label="是否有独立猫咪洗护区"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        
        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea
            placeholder="其它需要备注的信息"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新服务' : '添加服务'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ServiceForm; 