import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Space, Card, Divider, message } from 'antd';
import { brandAPI } from '../api/api';

const { TextArea } = Input;
const { Option } = Select;

const BrandForm = ({ productId, brandId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (brandId) {
      setIsEdit(true);
      fetchBrandData();
    } else {
      form.setFieldsValue({ product: productId });
    }
  }, [productId, brandId, form]);

  const fetchBrandData = async () => {
    setLoading(true);
    try {
      const response = await brandAPI.getById(brandId);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('Error fetching brand data:', error);
      message.error('获取品牌信息失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await brandAPI.update(brandId, values);
        message.success('品牌信息更新成功');
      } else {
        await brandAPI.create(values);
        message.success('品牌信息添加成功');
        form.resetFields();
        form.setFieldsValue({ product: productId });
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving brand:', error);
      message.error(isEdit ? '品牌信息更新失败' : '品牌信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  const categoryOptions = [
    { label: '猫粮', value: '猫粮' },
    { label: '狗粮', value: '狗粮' },
    { label: '猫砂', value: '猫砂' },
    { label: '清洁用品', value: '清洁用品' },
    { label: '宠物玩具', value: '宠物玩具' },
    { label: '宠物护理', value: '宠物护理' },
    { label: '宠物保健', value: '宠物保健' },
    { label: '宠物服饰', value: '宠物服饰' },
    { label: '其他', value: '其他' },
  ];

  const positioningOptions = [
    { label: '高端', value: 'high_end' },
    { label: '中端', value: 'mid_range' },
    { label: '大众/经济', value: 'economy' },
  ];

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <Form.Item name="product" hidden>
          <Input />
        </Form.Item>
        
        <Form.Item
          name="category"
          label="品类"
          rules={[{ required: true, message: '请选择品类' }]}
        >
          <Select placeholder="请选择品类">
            {categoryOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="brand_name"
          label="品牌名称"
          rules={[{ required: true, message: '请输入品牌名称' }]}
        >
          <Input placeholder="请输入品牌名称" />
        </Form.Item>
        
        <Form.Item
          name="positioning"
          label="品牌定位"
        >
          <Select placeholder="请选择品牌定位">
            {positioningOptions.map(option => (
              <Option key={option.value} value={option.value}>
                {option.label}
              </Option>
            ))}
          </Select>
        </Form.Item>
        
        <Form.Item
          name="price_range"
          label="价格区间"
        >
          <Input placeholder="例如：100-200元/kg" />
        </Form.Item>
        
        <Form.Item
          name="origin"
          label="产地"
        >
          <Input placeholder="品牌产地，如中国、美国、日本等" />
        </Form.Item>
        
        <Form.Item
          name="sales_volume"
          label="销量情况"
        >
          <Select placeholder="请选择销量情况">
            <Option value="high">销量好</Option>
            <Option value="average">一般</Option>
            <Option value="low">较差</Option>
            <Option value="unknown">未知</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name="notes"
          label="备注/特色"
        >
          <TextArea
            placeholder="请描述该品牌的特色或其他需要备注的信息"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新品牌' : '添加品牌'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default BrandForm; 