import React, { useState } from 'react';
import { Upload, Button, Select, Input, Form, Card, Row, Col, Tabs, message, Divider } from 'antd';
import { PlusOutlined } from '@ant-design/icons';

const { Option } = Select;
const { TabPane } = Tabs;

// 分类图片上传组件，专为添加门店时使用
const AddStoreCategorizedImages = ({ form, setImagesData }) => {
  const [activeCategory, setActiveCategory] = useState('exterior');
  
  // 图片分类信息
  const categoryGroups = {
    exterior: [
      { value: 'storefront', label: '门店正面/入口' },
      { value: 'window_display', label: '橱窗展示' },
      { value: 'surroundings', label: '周边环境' },
      { value: 'business_info', label: '营业时间/联系方式' },
    ],
    retail: [
      { value: 'retail_overview', label: '零售区整体布局' },
      { value: 'food_section', label: '主粮区' },
      { value: 'snack_section', label: '零食区' },
      { value: 'supplies_section', label: '用品区' },
      { value: 'grooming_products', label: '洗护/医疗保健品区' },
      { value: 'featured_products', label: '重点品牌/特色产品' },
      { value: 'price_tags', label: '价格标签/促销信息' },
      { value: 'checkout_area', label: '收银台区域' },
    ],
    grooming: [
      { value: 'grooming_overview', label: '洗护区全景' },
      { value: 'grooming_station', label: '单个洗护工位' },
      { value: 'waiting_area', label: '等待区/家长休息区' },
      { value: 'service_menu', label: '价目表/服务项目单' },
      { value: 'grooming_brands', label: '使用的洗护产品品牌' },
    ],
    other: [
      { value: 'brand_elements', label: '品牌元素/文化墙' },
      { value: 'special_facilities', label: '特色服务/设施' },
      { value: 'staff_interaction', label: '员工互动' },
      { value: 'cleanliness', label: '卫生清洁状况' },
      { value: 'other', label: '其他' },
    ]
  };

  // 获取表单中的图片数据
  const getImagesFieldValue = () => {
    return form.getFieldValue('categorizedImages') || {};
  };

  // 更新表单中的图片数据
  const updateImagesField = (category, fileList) => {
    const currentImages = getImagesFieldValue();
    const newImages = {
      ...currentImages,
      [category]: fileList
    };
    
    form.setFieldsValue({ categorizedImages: newImages });
    setImagesData(newImages);
  };

  // 处理图片选择变化
  const handleCategoryImageChange = (info, category) => {
    if (info && info.fileList) {
      updateImagesField(category, info.fileList);
    }
  };

  // 渲染每个类别的上传组件
  const renderCategoryUpload = (category) => {
    const imagesField = getImagesFieldValue();
    const fileList = imagesField[category.value] || [];
    
    const uploadButton = (
      <div>
        <PlusOutlined />
        <div style={{ marginTop: 8 }}>上传图片</div>
      </div>
    );

    return (
      <Col span={12} key={category.value}>
        <Card 
          title={category.label} 
          size="small" 
          style={{ marginBottom: 16 }}
        >
          <Upload
            listType="picture-card"
            fileList={fileList}
            beforeUpload={() => false}
            onChange={(info) => handleCategoryImageChange(info, category.value)}
            multiple
          >
            {fileList.length >= 5 ? null : uploadButton}
          </Upload>
          <div style={{ marginTop: 8 }}>
            <small>建议上传1-5张图片，首张作为该类别主图</small>
          </div>
        </Card>
      </Col>
    );
  };

  return (
    <>
      <Form.Item name="categorizedImages" hidden>
        <Input />
      </Form.Item>
    
      <Tabs activeKey={activeCategory} onChange={setActiveCategory}>
        <TabPane tab="门店外部照片" key="exterior">
          <Card>
            <Row gutter={[16, 16]}>
              {categoryGroups.exterior.map(category => 
                renderCategoryUpload(category)
              )}
            </Row>
          </Card>
        </TabPane>
      
        <TabPane tab="零售区域" key="retail">
          <Card>
            <Row gutter={[16, 16]}>
              {categoryGroups.retail.map(category => 
                renderCategoryUpload(category)
              )}
            </Row>
          </Card>
        </TabPane>
      
        <TabPane tab="洗护/服务区域" key="grooming">
          <Card>
            <Row gutter={[16, 16]}>
              {categoryGroups.grooming.map(category => 
                renderCategoryUpload(category)
              )}
            </Row>
          </Card>
        </TabPane>
      
        <TabPane tab="其他特色照片" key="other">
          <Card>
            <Row gutter={[16, 16]}>
              {categoryGroups.other.map(category => 
                renderCategoryUpload(category)
              )}
            </Row>
          </Card>
        </TabPane>
      </Tabs>
    </>
  );
};

export default AddStoreCategorizedImages; 