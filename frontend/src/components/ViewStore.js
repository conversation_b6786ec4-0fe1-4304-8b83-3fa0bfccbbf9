import React, { useState, useEffect } from 'react';
import { Tabs, Button, Descriptions, message, Spin, Card, Row, Col, Divider, Space, Tag, Image, Typography, Table, Empty } from 'antd';
import { ArrowLeftOutlined, EditOutlined, EnvironmentOutlined, PhoneOutlined, ClockCircleOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { storeAPI, storeImageAPI } from '../api/api';
import ServiceManager from './ServiceManager';
import ProductManager from './ProductManager';
import PricingManager from './PricingManager';
import EnvironmentManager from './EnvironmentManager';
import MarketingManager from './MarketingManager';
import StaffOperationsManager from './StaffOperationsManager';
import RatingManager from './RatingManager';
import ActivityManager from './ActivityManager';
import CategorizedImageUpload from './CategorizedImageUpload';
import AMapLocationPicker from './AMapLocationPicker';
import AMapDisplay from './AMapDisplay';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

const ViewStore = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [storeData, setStoreData] = useState(null);
  const [storeImages, setStoreImages] = useState([]);
  const [imageLoading, setImageLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("1");

  // 图片分类信息
  const categoryGroups = {
    exterior: {
      name: '门店外部',
      categories: [
        { value: 'storefront', label: '门店正面/入口' },
        { value: 'window_display', label: '橱窗展示' },
        { value: 'surroundings', label: '周边环境' },
        { value: 'business_info', label: '营业时间/联系方式' },
      ]
    },
    retail: {
      name: '零售区域',
      categories: [
        { value: 'retail_overview', label: '零售区整体布局' },
        { value: 'food_section', label: '主粮区' },
        { value: 'snack_section', label: '零食区' },
        { value: 'supplies_section', label: '用品区' },
        { value: 'grooming_products', label: '洗护/医疗保健品区' },
        { value: 'featured_products', label: '重点品牌/特色产品' },
        { value: 'price_tags', label: '价格标签/促销信息' },
        { value: 'checkout_area', label: '收银台区域' },
      ]
    },
    grooming: {
      name: '洗护/服务区域',
      categories: [
        { value: 'grooming_overview', label: '洗护区全景' },
        { value: 'grooming_station', label: '单个洗护工位' },
        { value: 'waiting_area', label: '等待区/家长休息区' },
        { value: 'service_menu', label: '价目表/服务项目单' },
        { value: 'grooming_brands', label: '使用的洗护产品品牌' },
      ]
    },
    other: {
      name: '其他特色',
      categories: [
        { value: 'brand_elements', label: '品牌元素/文化墙' },
        { value: 'special_facilities', label: '特色服务/设施' },
        { value: 'staff_interaction', label: '员工互动' },
        { value: 'cleanliness', label: '卫生清洁状况' },
        { value: 'other', label: '其他' },
      ]
    }
  };

  useEffect(() => {
    fetchStoreData();
    fetchStoreImages();
  }, [id]);

  const fetchStoreData = async () => {
    setLoading(true);
    try {
      const response = await storeAPI.getById(id);
      setStoreData(response.data);
    } catch (error) {
      console.error('Error fetching store data:', error);
      message.error('获取门店信息失败');
    } finally {
      setLoading(false);
    }
  };
  
  const fetchStoreImages = async () => {
    setImageLoading(true);
    try {
      const response = await storeImageAPI.getAll({ store: id });
      if (response.data && response.data.results) {
        setStoreImages(response.data.results);
      }
    } catch (error) {
      console.error('Error fetching store images:', error);
      message.error('获取门店图片失败');
    } finally {
      setImageLoading(false);
    }
  };
  
  // 将图片按分类组织
  const groupImagesByCategory = () => {
    const groupedImages = {};
    
    // 初始化所有分类
    Object.keys(categoryGroups).forEach(groupKey => {
      groupedImages[groupKey] = {};
      categoryGroups[groupKey].categories.forEach(category => {
        groupedImages[groupKey][category.value] = [];
      });
    });
    
    // 分组图片
    storeImages.forEach(image => {
      for (const groupKey of Object.keys(categoryGroups)) {
        const categoryFound = categoryGroups[groupKey].categories.find(
          category => category.value === image.category
        );
        
        if (categoryFound) {
          groupedImages[groupKey][image.category].push(image);
          break;
        }
      }
    });
    
    return groupedImages;
  };
  
  // 根据category值获取标签名称
  const getCategoryLabel = (categoryValue) => {
    for (const groupKey in categoryGroups) {
      const category = categoryGroups[groupKey].categories.find(
        cat => cat.value === categoryValue
      );
      if (category) {
        return category.label;
      }
    }
    return '未分类';
  };

  const renderStoreType = (type) => {
    const typeMap = {
      direct_chain: '直营连锁',
      franchise_chain: '加盟连锁',
      single_store: '单体店',
      pet_hospital: '宠物医院附带',
      retail_only: '纯零售店',
    };
    return typeMap[type] || type;
  };

  const renderCompetitionRelation = (relation) => {
    const relationMap = {
      direct_competitor: '直接竞争对手',
      indirect_hospital: '间接竞争对手-宠物医院',
      indirect_retail: '间接竞争对手-纯零售',
    };
    return relationMap[relation] || relation;
  };

  const renderMainBusiness = (business) => {
    const businessMap = {
      pet_grooming: '宠物洗护',
      pet_beauty: '宠物美容',
      pet_sale: '活体销售',
      exotic_pet_sale: '异宠销售',
      pet_supplies: '宠物用品',
      pet_fresh_food: '宠物鲜食',
      pet_training: '宠物训练',
      pet_diagnosis: '宠物诊疗',
      pet_funeral: '宠物殡葬',
      pet_cafe: '宠物咖啡',
      pet_playground: '宠物乐园',
      pet_event_planning: '宠物活动策划',
      pet_boarding: '宠物寄养',
      pet_photography: '宠物摄影',
      pet_shipping: '宠物托运',
      pet_rental: '宠物租赁',
      talent_training: '人才培训',
      pet_club: '宠物俱乐部',
      comprehensive: '综合服务',
    };
    
    // 如果是空值，返回"未指定"
    if (!business) return '未指定';
    
    // 如果是字符串但包含分号，说明是多个值
    if (typeof business === 'string' && business.includes(';')) {
      const businessList = business.split(';').filter(item => item.trim());
      return businessList.map(item => businessMap[item] || item).join('、');
    }
    
    // 如果是数组，处理数组中的每个值
    if (Array.isArray(business)) {
      return business.map(item => businessMap[item] || item).join('、');
    }
    
    // 单个值的情况
    return businessMap[business] || business;
  };


  const renderVisibility = (visibility) => {
    const visibilityMap = {
      excellent: '极佳',
      good: '良好',
      average: '一般',
      poor: '较差',
    };
    return visibilityMap[visibility] || visibility;
  };
  
  // 渲染分类图片组件
  const renderCategorizedImages = () => {
    if (imageLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '30px' }}>
          <Spin size="default" />
          <p>加载门店图片中...</p>
        </div>
      );
    } 
    
    if (storeImages.length === 0) {
      return <Empty description="暂无分类图片" />;
    }
    
    return (
      <>
        {Object.keys(categoryGroups).map(groupKey => {
          const group = categoryGroups[groupKey];
          const groupImages = storeImages.filter(img => {
            return group.categories.some(cat => cat.value === img.category);
          });
          
          if (groupImages.length === 0) {
            return null;
          }
          
          return (
            <div key={groupKey} style={{ marginBottom: 24 }}>
              <Title level={5} style={{ marginBottom: 16 }}>{group.name}</Title>
              
              {group.categories.map(category => {
                const images = storeImages.filter(img => img.category === category.value);
                
                if (images.length === 0) {
                  return null;
                }
                
                return (
                  <Card 
                    key={category.value}
                    size="small" 
                    title={`${category.label} (${images.length}张)`}
                    style={{ marginBottom: 16 }}
                  >
                    <Row gutter={[8, 16]}>
                      {images.map(image => (
                        <Col xs={24} sm={12} md={8} lg={6} key={image.id}>
                          <Image
                            src={image.image}
                            alt={image.caption || `${getCategoryLabel(image.category)} 图片`}
                            style={{ 
                              height: 150, 
                              objectFit: 'cover', 
                              width: '100%', 
                              borderRadius: 4 
                            }}
                          />
                        </Col>
                      ))}
                    </Row>
                  </Card>
                );
              })}
            </div>
          );
        })}
      </>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p>加载门店信息中...</p>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => navigate('/')}>返回</Button>
            <Title level={4} style={{ margin: 0 }}>{storeData.name}</Title>
            {storeData.brand_name && <Tag color="blue">{storeData.brand_name}</Tag>}
          </Space>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => navigate(`/edit-store/${id}`)}
          >
            编辑
          </Button>
        </div>
      </Card>
      
      <Divider style={{ margin: '16px 0' }} />
      
      <Tabs defaultActiveKey="1" onChange={setActiveTab} style={{ marginTop: 24 }}>
        <TabPane tab="基本信息" key="1">
          <Card>
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Descriptions title="门店基本信息" column={1} bordered>
                  <Descriptions.Item label="门店名称">{storeData.name}</Descriptions.Item>
                  <Descriptions.Item label="品牌名">{storeData.brand_name || '无'}</Descriptions.Item>
                  <Descriptions.Item label="门店类型">{renderStoreType(storeData.store_type)}</Descriptions.Item>
                  <Descriptions.Item label="主营业务">{renderMainBusiness(storeData.main_business)}</Descriptions.Item>
                  <Descriptions.Item label="竞争关系">{renderCompetitionRelation(storeData.competition_relation)}</Descriptions.Item>
                  <Descriptions.Item label="开业年份">{storeData.opening_year || '未知'}</Descriptions.Item>
                  <Descriptions.Item label="门店面积">{storeData.store_area ? `${storeData.store_area} m²` : '未知'}</Descriptions.Item>
                </Descriptions>
              </Col>
              
              <Col xs={24} md={12}>
                <Descriptions title="位置与联系方式" column={1} bordered>
                  <Descriptions.Item label={<><EnvironmentOutlined /> 详细地址</>}>{storeData.address}</Descriptions.Item>
                  <Descriptions.Item label="所属商圈/区域">{storeData.business_district || '未知'}</Descriptions.Item>
                  <Descriptions.Item label="可见性与易达性">{storeData.visibility ? renderVisibility(storeData.visibility) : '未知'}</Descriptions.Item>
                  <Descriptions.Item label={<><PhoneOutlined /> 联系电话</>}>
                    {storeData.phone1 || '无'}{storeData.phone2 ? `, ${storeData.phone2}` : ''}
                  </Descriptions.Item>
                  <Descriptions.Item label={<><ClockCircleOutlined /> 营业时间（工作日）</>}>
                    {storeData.business_hours_weekday || '未知'}
                  </Descriptions.Item>
                  <Descriptions.Item label={<><ClockCircleOutlined /> 营业时间（周末）</>}>
                    {storeData.business_hours_weekend || '未知'}
                  </Descriptions.Item>
                </Descriptions>
              </Col>
            </Row>
            
            {storeData.transportation_convenience && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>交通便利性</Title>
                <Text>{storeData.transportation_convenience}</Text>
              </div>
            )}
            
            {storeData.surrounding_community_type && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>周边社区类型</Title>
                <Text>{storeData.surrounding_community_type}</Text>
              </div>
            )}
            
            {storeData.notes && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>备注</Title>
                <Text>{storeData.notes}</Text>
              </div>
            )}
            
            {storeData.main_image && (
              <div style={{ marginTop: 24 }}>
                <Title level={5}>门店主照片</Title>
                <div style={{ display: 'flex', flexWrap: 'wrap', gap: 8 }}>
                  <Image
                    width={400}
                    src={storeData.main_image}
                    alt="门店主照片"
                  />
                </div>
              </div>
            )}

            {/* 门店位置地图 */}
            <div style={{ marginTop: 24 }}>
              <AMapDisplay 
                location={{ lng: storeData.longitude, lat: storeData.latitude }}
                address={storeData.address}
                title="门店位置"
                key={`map-display-${storeData.id}`}
              />
            </div>

            {/* 分类图片展示部分 */}
            <div style={{ marginTop: 24 }}>
              <Divider orientation="left">分类图片 ({storeImages.length}张)</Divider>
              {renderCategorizedImages()}
            </div>
          </Card>
        </TabPane>
        
        <TabPane tab="服务项目" key="2">
          <Card>
            {storeData.services && storeData.services.length > 0 ? (
              <Table
                dataSource={storeData.services}
                rowKey="id"
                pagination={false}
                columns={[
                  {
                    title: '服务类别',
                    dataIndex: 'service_category',
                    key: 'service_category',
                  },
                  {
                    title: '服务名称',
                    dataIndex: 'service_name',
                    key: 'service_name',
                  },
                  {
                    title: '价格/价格范围',
                    dataIndex: 'price_range',
                    key: 'price_range',
                  },
                  {
                    title: '会员价/套餐价',
                    dataIndex: 'member_price',
                    key: 'member_price',
                  },
                  {
                    title: '使用产品品牌',
                    dataIndex: 'product_brand',
                    key: 'product_brand',
                  },
                  {
                    title: '独立猫咪区',
                    dataIndex: 'has_cat_area',
                    key: 'has_cat_area',
                    render: (hasCatArea) => (hasCatArea ? '有' : '无'),
                  },
                ]}
                expandable={{
                  expandedRowRender: (record) => (
                    <div>
                      <p><strong>服务内容描述：</strong> {record.service_description || '无'}</p>
                      <p><strong>预约方式：</strong> {record.appointment_methods || '无'}</p>
                      <p><strong>等待区设施：</strong> {record.waiting_area_facilities || '无'}</p>
                      <p><strong>备注：</strong> {record.notes || '无'}</p>
                    </div>
                  ),
                }}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无服务项目数据</Text>
              </div>
            )}
          </Card>
        </TabPane>
        
        <TabPane tab="零售产品" key="3">
          <Card>
            {storeData.products && storeData.products.length > 0 ? (
              <>
                {storeData.products.map((product) => (
                  <div key={product.id}>
                    <Descriptions title="产品信息" bordered>
                      <Descriptions.Item label="主营产品大类" span={3}>
                        {product.main_product_categories || '无'}
                      </Descriptions.Item>
                      <Descriptions.Item label="产品丰富度评分">
                        {product.product_richness_score || '未评分'}
                      </Descriptions.Item>
                      <Descriptions.Item label="是否有特色/独家产品">
                        {product.has_special_products ? '是' : '否'}
                      </Descriptions.Item>
                      <Descriptions.Item label="特色产品描述" span={3}>
                        {product.special_products_description || '无'}
                      </Descriptions.Item>
                      <Descriptions.Item label="产品陈列与布局评价" span={3}>
                        {product.product_display_notes || '无'}
                      </Descriptions.Item>
                    </Descriptions>
                    
                    {product.brands && product.brands.length > 0 && (
                      <div style={{ marginTop: 16 }}>
                        <Title level={5}>品牌信息</Title>
                        <Table
                          dataSource={product.brands}
                          rowKey="id"
                          pagination={false}
                          columns={[
                            {
                              title: '品类',
                              dataIndex: 'category',
                              key: 'category',
                            },
                            {
                              title: '品牌名称',
                              dataIndex: 'brand_name',
                              key: 'brand_name',
                            },
                            {
                              title: '品牌定位',
                              dataIndex: 'positioning',
                              key: 'positioning',
                              render: (positioning) => {
                                const positioningMap = {
                                  high_end: '高端',
                                  mid_range: '中端',
                                  economy: '大众/经济',
                                };
                                return positioningMap[positioning] || positioning || '未知';
                              },
                            },
                            {
                              title: '备注/特色',
                              dataIndex: 'notes',
                              key: 'notes',
                            },
                          ]}
                        />
                      </div>
                    )}
                  </div>
                ))}
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无零售产品数据</Text>
              </div>
            )}
          </Card>
        </TabPane>
        
        <TabPane tab="价格与会员体系" key="4">
          <Card>
            {storeData.pricing ? (
              <>
                <Descriptions title="价格策略" bordered>
                  <Descriptions.Item label="整体价格定位">
                    {(() => {
                      const priceLevelMap = {
                        high_end: '高端',
                        mid_high: '中高端',
                        mid_range: '中端',
                        mid_low: '中低端',
                        economy: '经济型',
                      };
                      return priceLevelMap[storeData.pricing.price_level] || storeData.pricing.price_level || '未知';
                    })()}
                  </Descriptions.Item>
                  <Descriptions.Item label="价格透明度">
                    {(() => {
                      const transparencyMap = {
                        transparent: '公开透明',
                        partially: '部分公开',
                        inquiry: '需咨询',
                      };
                      return transparencyMap[storeData.pricing.price_transparency] || storeData.pricing.price_transparency || '未知';
                    })()}
                  </Descriptions.Item>
                  <Descriptions.Item label="是否有会员制度">
                    {storeData.pricing.has_membership ? '是' : '否'}
                  </Descriptions.Item>
                  <Descriptions.Item label="会员卡类型" span={3}>
                    {storeData.pricing.membership_types || '无'}
                  </Descriptions.Item>
                  <Descriptions.Item label="办理门槛/费用" span={3}>
                    {storeData.pricing.membership_threshold || '无'}
                  </Descriptions.Item>
                  <Descriptions.Item label="会员主要权益" span={3}>
                    {storeData.pricing.membership_benefits || '无'}
                  </Descriptions.Item>
                  <Descriptions.Item label="会员体系吸引力评估">
                    {storeData.pricing.membership_attractiveness || '未评估'}
                  </Descriptions.Item>
                  <Descriptions.Item label="促销活动频率">
                    {(() => {
                      const frequencyMap = {
                        frequent: '频繁',
                        quite_often: '较多',
                        average: '一般',
                        rare: '较少',
                        never: '几乎没有',
                      };
                      return frequencyMap[storeData.pricing.promotion_frequency] || storeData.pricing.promotion_frequency || '未知';
                    })()}
                  </Descriptions.Item>
                  <Descriptions.Item label="常规促销活动类型" span={3}>
                    {storeData.pricing.promotion_types || '无'}
                  </Descriptions.Item>
                </Descriptions>
                
                {storeData.pricing.benchmark_products && storeData.pricing.benchmark_products.length > 0 && (
                  <div style={{ marginTop: 16 }}>
                    <Title level={5}>标杆商品比价</Title>
                    <Table
                      dataSource={storeData.pricing.benchmark_products}
                      rowKey="id"
                      pagination={false}
                      columns={[
                        {
                          title: '商品名称',
                          dataIndex: 'product_name',
                          key: 'product_name',
                        },
                        {
                          title: '本店售价',
                          dataIndex: 'store_price',
                          key: 'store_price',
                        },
                        {
                          title: '竞对售价',
                          dataIndex: 'competitor_price',
                          key: 'competitor_price',
                        },
                        {
                          title: '差价',
                          dataIndex: 'price_difference',
                          key: 'price_difference',
                        },
                      ]}
                    />
                  </div>
                )}
              </>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无价格与会员体系数据</Text>
              </div>
            )}
          </Card>
        </TabPane>
        
        <TabPane tab="门店环境与设施" key="5">
          <Card>
            {storeData.environment ? (
              <Descriptions title="环境与设施" bordered>
                <Descriptions.Item label="门店清洁度">
                  {storeData.environment.cleanliness ? `${storeData.environment.cleanliness} 分` : '未评分'}
                </Descriptions.Item>
                <Descriptions.Item label="店内气味">
                  {(() => {
                    const smellMap = {
                      fresh: '清新无异味',
                      light: '轻微宠物味',
                      moderate: '较重宠物味',
                      unpleasant: '难闻',
                    };
                    return smellMap[storeData.environment.smell] || storeData.environment.smell || '未知';
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label="装修风格">
                  {storeData.environment.decoration_style || '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="空间布局合理性">
                  {storeData.environment.layout_rationality ? `${storeData.environment.layout_rationality} 分` : '未评分'}
                </Descriptions.Item>
                <Descriptions.Item label="采光情况">
                  {(() => {
                    const lightingMap = {
                      very_bright: '非常明亮',
                      bright: '明亮',
                      average: '一般',
                      dim: '偏暗',
                    };
                    return lightingMap[storeData.environment.lighting] || storeData.environment.lighting || '未知';
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label="通风情况">
                  {(() => {
                    const ventilationMap = {
                      good: '良好',
                      average: '一般',
                      poor: '较差',
                    };
                    return ventilationMap[storeData.environment.ventilation] || storeData.environment.ventilation || '未知';
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label="美容区设备情况" span={3}>
                  {storeData.environment.beauty_equipment || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="零售区货架与陈列" span={3}>
                  {storeData.environment.retail_display || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="顾客便利设施" span={3}>
                  {storeData.environment.customer_facilities || '无'}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无门店环境与设施数据</Text>
              </div>
            )}
          </Card>
        </TabPane>
        
        <TabPane tab="营销与口碑" key="6">
          <Card>
            {storeData.marketing ? (
              <Descriptions title="营销与口碑" bordered>
                <Descriptions.Item label="官方网站">
                  {storeData.marketing.website_url ? <a href={storeData.marketing.website_url} target="_blank" rel="noopener noreferrer">{storeData.marketing.website_url}</a> : '无'}
                </Descriptions.Item>
                <Descriptions.Item label="微信公众号">
                  {storeData.marketing.wechat_account || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="微信小程序">
                  {storeData.marketing.wechat_miniapp || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="小红书账号">
                  {storeData.marketing.xiaohongshu_account || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="抖音账号">
                  {storeData.marketing.douyin_account || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="线上活跃度">
                  {(() => {
                    const activityMap = {
                      very_active: '非常活跃',
                      active: '活跃',
                      average: '一般',
                      inactive: '不活跃',
                      none: '基本无',
                    };
                    return activityMap[storeData.marketing.online_activity] || storeData.marketing.online_activity || '未知';
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label="大众点评链接">
                  {storeData.marketing.dianping_url ? <a href={storeData.marketing.dianping_url} target="_blank" rel="noopener noreferrer">查看店铺</a> : '无'}
                </Descriptions.Item>
                <Descriptions.Item label="大众点评评分">
                  {storeData.marketing.dianping_score || '无'} {storeData.marketing.dianping_reviews ? `(${storeData.marketing.dianping_reviews} 条评论)` : ''}
                </Descriptions.Item>
                <Descriptions.Item label="美团链接">
                  {storeData.marketing.meituan_url ? <a href={storeData.marketing.meituan_url} target="_blank" rel="noopener noreferrer">查看店铺</a> : '无'}
                </Descriptions.Item>
                <Descriptions.Item label="美团评分">
                  {storeData.marketing.meituan_score || '无'} {storeData.marketing.meituan_reviews ? `(${storeData.marketing.meituan_reviews} 条评论)` : ''}
                </Descriptions.Item>
                <Descriptions.Item label="主要好评关键词" span={3}>
                  {storeData.marketing.positive_keywords || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="主要差评关键词" span={3}>
                  {storeData.marketing.negative_keywords || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="线下营销活动记录" span={3}>
                  {storeData.marketing.offline_marketing || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="品牌形象感知" span={3}>
                  {storeData.marketing.brand_perception || '无'}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无营销与口碑数据</Text>
              </div>
            )}
          </Card>
        </TabPane>
        
        <TabPane tab="人员与运营" key="7">
          <Card>
            {storeData.staff ? (
              <Descriptions title="人员与运营" bordered>
                <Descriptions.Item label="预估员工总数">
                  {storeData.staff.total_staff || '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="美容师数量">
                  {storeData.staff.beauticians || '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="员工服务态度">
                  {storeData.staff.service_attitude ? `${storeData.staff.service_attitude} 分` : '未评分'}
                </Descriptions.Item>
                <Descriptions.Item label="员工专业知识水平">
                  {storeData.staff.professional_knowledge ? `${storeData.staff.professional_knowledge} 分` : '未评分'}
                </Descriptions.Item>
                <Descriptions.Item label="预约饱满度">
                  {(() => {
                    const fullnessMap = {
                      very_difficult: '很难约',
                      days_ahead: '需提前几天',
                      easy: '较容易约',
                      anytime: '随时可约',
                    };
                    return fullnessMap[storeData.staff.appointment_fullness] || storeData.staff.appointment_fullness || '未知';
                  })()}
                </Descriptions.Item>
                <Descriptions.Item label="运营效率感知">
                  {storeData.staff.operational_efficiency ? `${storeData.staff.operational_efficiency} 分` : '未评分'}
                </Descriptions.Item>
                <Descriptions.Item label="客流量观察" span={3}>
                  {storeData.staff.customer_flow || '无'}
                </Descriptions.Item>
              </Descriptions>
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无人员与运营数据</Text>
              </div>
            )}
          </Card>
        </TabPane>
        
        <TabPane tab="综合评分" key="8">
          <Card>
            <RatingManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="图片管理" key="9">
          <Card>
            <CategorizedImageUpload 
              storeId={id} 
              onSuccess={() => {
                message.success('图片管理操作成功');
                fetchStoreImages(); // 更新图片后刷新
              }} 
            />
          </Card>
        </TabPane>
        
        <TabPane tab="活动追踪" key="10">
          <Card>
            <ActivityManager storeId={id} />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ViewStore; 