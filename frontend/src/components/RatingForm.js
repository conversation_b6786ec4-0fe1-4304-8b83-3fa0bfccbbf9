import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Space, Card, Divider, message, Row, Col, Rate, InputNumber } from 'antd';
import { ratingAPI } from '../api/api';

const { TextArea } = Input;

const RatingForm = ({ storeId, ratingId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (ratingId) {
      setIsEdit(true);
      fetchRatingData();
    } else {
      form.setFieldsValue({ store: storeId });
    }
  }, [storeId, ratingId, form]);

  const fetchRatingData = async () => {
    setLoading(true);
    try {
      const response = await ratingAPI.getById(ratingId);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('Error fetching rating data:', error);
      message.error('获取综合评分信息失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await ratingAPI.update(ratingId, values);
        message.success('综合评分信息更新成功');
      } else {
        await ratingAPI.create(values);
        message.success('综合评分信息添加成功');
        form.resetFields();
        form.setFieldsValue({ store: storeId });
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving rating data:', error);
      message.error(isEdit ? '综合评分信息更新失败' : '综合评分信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          service_quality_rating: 3.5,
          product_variety_rating: 3.5,
          price_value_rating: 3.5,
          environment_rating: 3.5,
          staff_professionalism_rating: 3.5,
          overall_rating: 3.5,
        }}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="service_quality_rating"
              label="服务质量评分"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="product_variety_rating"
              label="产品多样性评分"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="price_value_rating"
              label="价格/性价比评分"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="environment_rating"
              label="环境舒适度评分"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="staff_professionalism_rating"
              label="员工专业度评分"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="overall_rating"
              label="综合评分"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="market_positioning"
          label="市场定位评估"
        >
          <TextArea
            placeholder="请评估该店铺的市场定位，如目标客户群体、市场份额等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="competitive_advantages"
          label="竞争优势"
        >
          <TextArea
            placeholder="请分析该店铺的主要竞争优势"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="competitive_disadvantages"
          label="竞争劣势"
        >
          <TextArea
            placeholder="请分析该店铺的主要竞争劣势"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="customer_loyalty_assessment"
          label="顾客忠诚度评估"
        >
          <TextArea
            placeholder="请评估该店铺的顾客忠诚度情况"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="market_influence_score"
              label="市场影响力评分 (1-10分)"
            >
              <InputNumber
                min={1}
                max={10}
                style={{ width: '100%' }}
                placeholder="请为市场影响力打分，1-10分"
              />
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="future_potential_score"
              label="未来发展潜力评分 (1-10分)"
            >
              <InputNumber
                min={1}
                max={10}
                style={{ width: '100%' }}
                placeholder="请为未来发展潜力打分，1-10分"
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="threat_assessment"
          label="威胁程度评估"
        >
          <TextArea
            placeholder="请评估该店铺对贵司的威胁程度"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="improvement_suggestions"
          label="改进建议"
        >
          <TextArea
            placeholder="基于对该竞争对手的分析，您对贵司有何改进建议"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>
        
        <Form.Item
          name="notes"
          label="其他备注"
        >
          <TextArea
            placeholder="其它需要备注的信息"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新综合评分' : '添加综合评分'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default RatingForm; 