import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, message, Spin, Descriptions, Space } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { staffAPI } from '../api/api';
import StaffOperationsForm from './StaffOperationsForm';

const { Title, Text } = Typography;

const StaffOperationsManager = ({ storeId }) => {
  const [loading, setLoading] = useState(true);
  const [staffData, setStaffData] = useState(null);
  const [isEditing, setIsEditing] = useState(false);

  useEffect(() => {
    if (storeId) {
      fetchStaffData();
    }
  }, [storeId]);

  const fetchStaffData = async () => {
    setLoading(true);
    try {
      const response = await staffAPI.getAll({ store: storeId });
      const staffOperationsData = response.data.results || [];
      
      if (staffOperationsData.length > 0) {
        setStaffData(staffOperationsData[0]);
      } else {
        setStaffData(null);
      }
    } catch (error) {
      console.error('Error fetching staff operations data:', error);
      message.error('获取人员与运营信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddStaffOperations = () => {
    setIsEditing(true);
  };

  const handleEditStaffOperations = () => {
    setIsEditing(true);
  };

  const handleStaffOperationsSaved = () => {
    setIsEditing(false);
    fetchStaffData();
  };

  const renderStaffAttitudeText = (attitude) => {
    const attitudeMap = {
      excellent: '非常好',
      good: '良好',
      average: '一般',
      poor: '较差',
    };
    return attitudeMap[attitude] || attitude;
  };

  const renderGroomerSkillLevelText = (level) => {
    const levelMap = {
      high: '高水平',
      average: '中等水平',
      low: '入门水平',
      mixed: '水平参差不齐',
    };
    return levelMap[level] || level;
  };

  const renderStaffTrainingText = (training) => {
    const trainingMap = {
      regular: '定期系统培训',
      occasional: '偶尔培训',
      rare: '很少培训',
      unknown: '不清楚',
    };
    return trainingMap[training] || training;
  };

  const renderStaffTurnoverText = (turnover) => {
    const turnoverMap = {
      high: '较高',
      moderate: '一般',
      low: '较低',
      unknown: '不清楚',
    };
    return turnoverMap[turnover] || turnover;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
        <p>加载人员与运营信息...</p>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div>
        <Title level={4}>
          {staffData ? '编辑人员与运营' : '添加人员与运营'}
        </Title>
        <StaffOperationsForm
          storeId={storeId}
          staffId={staffData?.id}
          onSuccess={handleStaffOperationsSaved}
        />
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button onClick={() => setIsEditing(false)}>取消</Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Title level={4}>人员与运营管理</Title>
        {staffData ? (
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={handleEditStaffOperations}
          >
            编辑人员与运营信息
          </Button>
        ) : (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={handleAddStaffOperations}
          >
            添加人员与运营信息
          </Button>
        )}
      </div>
      
      {staffData ? (
        <Card>
          <Descriptions title="人员与运营" bordered>
            <Descriptions.Item label="总员工数">
              {staffData.total_staff || '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="美容师数量">
              {staffData.groomers || '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="销售人员数量">
              {staffData.salespeople || '未知'}
            </Descriptions.Item>
            
            <Descriptions.Item label="员工服务态度">
              {staffData.staff_attitude ? renderStaffAttitudeText(staffData.staff_attitude) : '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="员工服务质量">
              {staffData.employee_service_quality ? `${staffData.employee_service_quality} 分` : '未评分'}
            </Descriptions.Item>
            <Descriptions.Item label="美容师技术水平">
              {staffData.groomer_skill_level ? renderGroomerSkillLevelText(staffData.groomer_skill_level) : '未知'}
            </Descriptions.Item>
            
            <Descriptions.Item label="员工培训情况">
              {staffData.staff_training ? renderStaffTrainingText(staffData.staff_training) : '未知'}
            </Descriptions.Item>
            <Descriptions.Item label="员工流动性">
              {staffData.staff_turnover ? renderStaffTurnoverText(staffData.staff_turnover) : '未知'}
            </Descriptions.Item>
            
            {staffData.management_structure && (
              <Descriptions.Item label="管理架构" span={3}>
                {staffData.management_structure}
              </Descriptions.Item>
            )}
            
            {staffData.operational_efficiency && (
              <Descriptions.Item label="运营效率" span={3}>
                {staffData.operational_efficiency}
              </Descriptions.Item>
            )}
            
            {staffData.customer_appointment_system && (
              <Descriptions.Item label="顾客预约系统" span={3}>
                {staffData.customer_appointment_system}
              </Descriptions.Item>
            )}
            
            {staffData.workload_description && (
              <Descriptions.Item label="工作量情况" span={3}>
                {staffData.workload_description}
              </Descriptions.Item>
            )}
            
            {staffData.notes && (
              <Descriptions.Item label="其他备注" span={3}>
                {staffData.notes}
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
      ) : (
        <div style={{ textAlign: 'center', padding: '30px' }}>
          <Text type="secondary">暂无人员与运营信息，请点击"添加人员与运营信息"按钮添加</Text>
        </div>
      )}
    </div>
  );
};

export default StaffOperationsManager; 