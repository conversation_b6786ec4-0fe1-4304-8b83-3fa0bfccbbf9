import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Typography, 
  Button, 
  message, 
  Spin, 
  Table, 
  Tag, 
  Space, 
  Popconfirm,
  Badge,
  Tooltip,
  Drawer,
  Empty
} from 'antd';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined,
  InfoCircleOutlined,
  AlertOutlined,
  CalendarOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { activityAPI } from '../api/api';
import ActivityForm from './ActivityForm';
import moment from 'moment';

const { Title, Text, Paragraph } = Typography;

const ActivityManager = ({ storeId }) => {
  const [loading, setLoading] = useState(true);
  const [activities, setActivities] = useState([]);
  const [isAddDrawerVisible, setIsAddDrawerVisible] = useState(false);
  const [isEditDrawerVisible, setIsEditDrawerVisible] = useState(false);
  const [isViewDrawerVisible, setIsViewDrawerVisible] = useState(false);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [drawerTitle, setDrawerTitle] = useState('');

  useEffect(() => {
    if (storeId) {
      fetchActivities();
    }
  }, [storeId]);

  const fetchActivities = async () => {
    setLoading(true);
    try {
      const response = await activityAPI.getAll({ store: storeId });
      const activitiesData = response.data.results || [];
      setActivities(activitiesData);
    } catch (error) {
      console.error('Error fetching activities:', error);
      message.error('获取竞品活动信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddActivity = () => {
    setDrawerTitle('添加竞品活动');
    setIsAddDrawerVisible(true);
  };

  const handleEditActivity = (activity) => {
    setCurrentActivity(activity);
    setDrawerTitle('编辑竞品活动');
    setIsEditDrawerVisible(true);
  };
  
  const handleViewActivity = (activity) => {
    setCurrentActivity(activity);
    setDrawerTitle('查看竞品活动详情');
    setIsViewDrawerVisible(true);
  };

  const handleDeleteActivity = async (activityId) => {
    try {
      await activityAPI.delete(activityId);
      message.success('活动已删除');
      fetchActivities();
    } catch (error) {
      console.error('Error deleting activity:', error);
      message.error('删除活动失败');
    }
  };

  const handleActivitySaved = () => {
    setIsAddDrawerVisible(false);
    setIsEditDrawerVisible(false);
    fetchActivities();
  };

  const renderActivityType = (type) => {
    const typeMap = {
      promotion: '促销活动',
      event: '营销活动/事件',
      new_product: '新品发布',
      price_change: '价格变动',
      service_change: '服务调整',
      store_change: '门店变动',
      other: '其他',
    };
    return typeMap[type] || type;
  };

  const renderActivityStatus = (status) => {
    const statusMap = {
      upcoming: '即将开始',
      active: '进行中',
      ended: '已结束',
      recurring: '周期性',
    };
    return statusMap[status] || status;
  };

  const renderImpactLevel = (level) => {
    const levelMap = {
      high: '高影响',
      medium: '中等影响',
      low: '低影响',
      unknown: '影响未知',
    };
    return levelMap[level] || level;
  };

  const getActivityTypeIcon = (type) => {
    const iconMap = {
      promotion: <DollarOutlined />,
      event: <CalendarOutlined />,
      new_product: <InfoCircleOutlined />,
      price_change: <DollarOutlined />,
      service_change: <InfoCircleOutlined />,
      store_change: <InfoCircleOutlined />,
      other: <InfoCircleOutlined />,
    };
    return iconMap[type] || <InfoCircleOutlined />;
  };

  const getActivityStatusColor = (status) => {
    const colorMap = {
      upcoming: 'blue',
      active: 'green',
      ended: 'gray',
      recurring: 'purple',
    };
    return colorMap[status] || 'default';
  };

  const getImpactLevelColor = (level) => {
    const colorMap = {
      high: 'red',
      medium: 'orange',
      low: 'green',
      unknown: 'gray',
    };
    return colorMap[level] || 'default';
  };

  const columns = [
    {
      title: '活动标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <Space>
          {getActivityTypeIcon(record.activity_type)}
          <a onClick={() => handleViewActivity(record)}>{text}</a>
        </Space>
      ),
    },
    {
      title: '活动类型',
      dataIndex: 'activity_type',
      key: 'activity_type',
      render: (type) => renderActivityType(type),
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date) => date ? moment(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: '结束日期',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date) => date ? moment(date).format('YYYY-MM-DD') : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge 
          status={status === 'active' ? 'processing' : 'default'} 
          text={<Tag color={getActivityStatusColor(status)}>{renderActivityStatus(status)}</Tag>}
        />
      ),
    },
    {
      title: '影响级别',
      dataIndex: 'impact_level',
      key: 'impact_level',
      render: (level) => (
        <Tag color={getImpactLevelColor(level)}>
          {level === 'high' && <AlertOutlined />} {renderImpactLevel(level)}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              size="small" 
              icon={<EyeOutlined />} 
              onClick={() => handleViewActivity(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              size="small" 
              icon={<EditOutlined />}
              onClick={() => handleEditActivity(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个活动吗？"
              onConfirm={() => handleDeleteActivity(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                type="text" 
                danger 
                size="small" 
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  const renderActivityDetail = () => {
    if (!currentActivity) return null;
    
    return (
      <div>
        <Paragraph>
          <Title level={4}>{currentActivity.title}</Title>
          <Space>
            <Tag color={getActivityStatusColor(currentActivity.status)}>
              {renderActivityStatus(currentActivity.status)}
            </Tag>
            <Tag>
              {renderActivityType(currentActivity.activity_type)}
            </Tag>
            <Tag color={getImpactLevelColor(currentActivity.impact_level)}>
              {renderImpactLevel(currentActivity.impact_level)}
            </Tag>
          </Space>
        </Paragraph>
        
        <Paragraph>
          <Text strong>活动时间：</Text>
          {currentActivity.start_date && moment(currentActivity.start_date).format('YYYY-MM-DD')}
          {' 至 '}
          {currentActivity.end_date && moment(currentActivity.end_date).format('YYYY-MM-DD')}
        </Paragraph>
        
        <Paragraph>
          <Text strong>活动描述：</Text>
          <br />
          {currentActivity.description}
        </Paragraph>
        
        {currentActivity.discount_amount && (
          <Paragraph>
            <Text strong>折扣力度：</Text> {currentActivity.discount_amount}
          </Paragraph>
        )}
        
        {currentActivity.target_customers && (
          <Paragraph>
            <Text strong>目标客户群体：</Text> {currentActivity.target_customers}
          </Paragraph>
        )}
        
        {currentActivity.participation_threshold && (
          <Paragraph>
            <Text strong>参与门槛：</Text>
            <br />
            {currentActivity.participation_threshold}
          </Paragraph>
        )}
        
        {currentActivity.marketing_channels && (
          <Paragraph>
            <Text strong>营销渠道：</Text>
            <br />
            {currentActivity.marketing_channels}
          </Paragraph>
        )}
        
        {currentActivity.response_strategy && (
          <Paragraph>
            <Text strong>应对策略：</Text>
            <br />
            {currentActivity.response_strategy}
          </Paragraph>
        )}
        
        {currentActivity.results && (
          <Paragraph>
            <Text strong>活动效果：</Text>
            <br />
            {currentActivity.results}
          </Paragraph>
        )}
        
        {currentActivity.notes && (
          <Paragraph>
            <Text strong>备注：</Text>
            <br />
            {currentActivity.notes}
          </Paragraph>
        )}
      </div>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
        <p>加载竞品活动信息...</p>
      </div>
    );
  }

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Title level={4}>竞品活动追踪</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddActivity}
        >
          添加竞品活动
        </Button>
      </div>
      
      {activities.length > 0 ? (
        <Card>
          <Table 
            columns={columns} 
            dataSource={activities} 
            rowKey="id"
            pagination={{ pageSize: 5 }}
          />
        </Card>
      ) : (
        <Card>
          <Empty 
            description="暂无竞品活动信息" 
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button 
              type="primary" 
              icon={<PlusOutlined />}
              onClick={handleAddActivity}
            >
              添加竞品活动
            </Button>
          </Empty>
        </Card>
      )}
      
      {/* 添加活动抽屉 */}
      <Drawer
        title={drawerTitle}
        width={720}
        onClose={() => setIsAddDrawerVisible(false)}
        open={isAddDrawerVisible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        <ActivityForm 
          storeId={storeId}
          onSuccess={handleActivitySaved}
        />
      </Drawer>
      
      {/* 编辑活动抽屉 */}
      <Drawer
        title={drawerTitle}
        width={720}
        onClose={() => setIsEditDrawerVisible(false)}
        open={isEditDrawerVisible}
        bodyStyle={{ paddingBottom: 80 }}
      >
        {currentActivity && (
          <ActivityForm 
            storeId={storeId}
            activityId={currentActivity.id}
            onSuccess={handleActivitySaved}
          />
        )}
      </Drawer>
      
      {/* 查看活动抽屉 */}
      <Drawer
        title={drawerTitle}
        width={600}
        onClose={() => setIsViewDrawerVisible(false)}
        open={isViewDrawerVisible}
        extra={
          <Space>
            <Button 
              type="primary" 
              onClick={() => {
                setIsViewDrawerVisible(false);
                handleEditActivity(currentActivity);
              }}
            >
              编辑
            </Button>
          </Space>
        }
      >
        {renderActivityDetail()}
      </Drawer>
    </div>
  );
};

export default ActivityManager; 