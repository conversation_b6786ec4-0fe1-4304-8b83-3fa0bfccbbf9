import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Switch, Space, Card, Divider, message, Row, Col, InputNumber } from 'antd';
import { pricingAPI } from '../api/api';

const { TextArea } = Input;
const { Option } = Select;

const PricingForm = ({ storeId, pricingId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [hasMembership, setHasMembership] = useState(false);

  useEffect(() => {
    if (pricingId) {
      setIsEdit(true);
      fetchPricingData();
    } else {
      form.setFieldsValue({ 
        store: storeId,
        has_membership: false,
      });
    }
  }, [storeId, pricingId, form]);

  const fetchPricingData = async () => {
    setLoading(true);
    try {
      const response = await pricingAPI.getById(pricingId);
      const pricingData = response.data;
      
      // Set local state based on fetched data
      setHasMembership(pricingData.has_membership);
      
      form.setFieldsValue(pricingData);
    } catch (error) {
      console.error('Error fetching pricing data:', error);
      message.error('获取价格与会员体系信息失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await pricingAPI.update(pricingId, values);
        message.success('价格与会员体系信息更新成功');
      } else {
        await pricingAPI.create(values);
        message.success('价格与会员体系信息添加成功');
        form.resetFields();
        form.setFieldsValue({ 
          store: storeId,
          has_membership: false,
        });
        setHasMembership(false);
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving pricing:', error);
      message.error(isEdit ? '价格与会员体系信息更新失败' : '价格与会员体系信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  const handleMembershipChange = (checked) => {
    setHasMembership(checked);
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          has_membership: false,
          price_level: 'mid_range',
          price_transparency: 'partially',
          promotion_frequency: 'average',
        }}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="price_level"
              label="整体价格定位"
              rules={[{ required: true, message: '请选择整体价格定位' }]}
            >
              <Select placeholder="请选择整体价格定位">
                <Option value="high_end">高端</Option>
                <Option value="mid_high">中高端</Option>
                <Option value="mid_range">中端</Option>
                <Option value="mid_low">中低端</Option>
                <Option value="economy">经济型</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="price_transparency"
              label="价格透明度"
            >
              <Select placeholder="请选择价格透明度">
                <Option value="transparent">公开透明</Option>
                <Option value="partially">部分公开</Option>
                <Option value="inquiry">需咨询</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="has_membership"
          label="是否有会员制度"
          valuePropName="checked"
        >
          <Switch onChange={handleMembershipChange} />
        </Form.Item>
        
        {hasMembership && (
          <>
            <Form.Item
              name="membership_types"
              label="会员卡类型"
            >
              <TextArea
                placeholder="请描述会员卡类型，如普通会员卡、金卡、黑卡等"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
            
            <Form.Item
              name="membership_threshold"
              label="办理门槛/费用"
            >
              <TextArea
                placeholder="请描述办理会员卡的门槛或费用"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
            
            <Form.Item
              name="membership_benefits"
              label="会员主要权益"
            >
              <TextArea
                placeholder="请描述会员可享受的主要权益，如折扣、积分、专属服务等"
                autoSize={{ minRows: 2, maxRows: 4 }}
              />
            </Form.Item>
            
            <Form.Item
              name="membership_attractiveness"
              label="会员体系吸引力评估"
            >
              <Select placeholder="请选择会员体系吸引力">
                <Option value="very_attractive">非常有吸引力</Option>
                <Option value="attractive">有吸引力</Option>
                <Option value="average">一般</Option>
                <Option value="unattractive">吸引力较弱</Option>
                <Option value="poor">几乎无吸引力</Option>
              </Select>
            </Form.Item>
          </>
        )}
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="promotion_frequency"
              label="促销活动频率"
            >
              <Select placeholder="请选择促销活动频率">
                <Option value="frequent">频繁</Option>
                <Option value="quite_often">较多</Option>
                <Option value="average">一般</Option>
                <Option value="rare">较少</Option>
                <Option value="never">几乎没有</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="promotion_types"
          label="常规促销活动类型"
        >
          <TextArea
            placeholder="请描述常见的促销活动类型，如折扣、满减、买赠等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="pricing_strategy_notes"
          label="价格策略评价"
        >
          <TextArea
            placeholder="请评价该店铺的整体价格策略，如与市场定位是否匹配、价格区间等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <div style={{ marginBottom: 16 }}>
          <h3>标杆商品比价（选填）</h3>
          <p>请在此处添加用于比较的标杆商品价格（功能开发中）</p>
        </div>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新价格信息' : '添加价格信息'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default PricingForm; 