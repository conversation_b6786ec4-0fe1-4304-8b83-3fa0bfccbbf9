import React, { useState, useEffect } from 'react';
import { Form, Button, message, Spin, Card, Space, Divider, Tabs, Input, Select, InputNumber, Row, Col, Typography, Upload, Alert, Empty } from 'antd';
import { ArrowLeftOutlined, SaveOutlined, PlusOutlined, InfoCircleOutlined, UploadOutlined, DeleteOutlined } from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { storeAPI, storeImageAPI } from '../api/api';
import AMapLocationPicker from './AMapLocationPicker';
import ServiceManager from './ServiceManager';
import ProductManager from './ProductManager';
import PricingManager from './PricingManager';
import EnvironmentManager from './EnvironmentManager';
import MarketingManager from './MarketingManager';
import StaffOperationsManager from './StaffOperationsManager';
import RatingManager from './RatingManager';
import ActivityManager from './ActivityManager';

const { TabPane } = Tabs;
const { TextArea } = Input;
const { Title, Text } = Typography;
const { Option } = Select;

const EditStore = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('1');
  
  // 用于主照片的状态
  const [mainPhoto, setMainPhoto] = useState(null);
  
  // 当前选择的图片分类
  const [currentCategory, setCurrentCategory] = useState(null);
  
  // 分类图片的状态
  const [categoryImages, setCategoryImages] = useState({
    storefront: [], // 门店正面/入口
    window_display: [], // 橱窗展示
    surroundings: [], // 周边环境
    business_info: [], // 营业时间/联系方式
    retail_overview: [], // 零售区整体布局
    food_section: [], // 主粮区
    snack_section: [], // 零食区
    supplies_section: [], // 用品区
    grooming_products: [], // 洗护/医疗保健品区
    featured_products: [], // 重点品牌/特色产品
    price_tags: [], // 价格标签/促销信息
    checkout_area: [], // 收银台区域
    grooming_overview: [], // 洗护区全景
    grooming_station: [], // 单个洗护工位
    waiting_area: [], // 等待区/家长休息区
    service_menu: [], // 价目表/服务项目单
    grooming_brands: [], // 使用的洗护产品品牌
    brand_elements: [], // 品牌元素/文化墙
    special_facilities: [], // 特色服务/设施
    staff_interaction: [], // 员工互动
    cleanliness: [], // 卫生清洁状况
    other: [] // 其他
  });
  
  // 图片分类信息
  const categoryGroups = {
    exterior: {
      name: '门店外部',
      categories: [
        { value: 'storefront', label: '门店正面/入口' },
        { value: 'window_display', label: '橱窗展示' },
        { value: 'surroundings', label: '周边环境' },
        { value: 'business_info', label: '营业时间/联系方式' },
      ]
    },
    retail: {
      name: '零售区域',
      categories: [
        { value: 'retail_overview', label: '零售区整体布局' },
        { value: 'food_section', label: '主粮区' },
        { value: 'snack_section', label: '零食区' },
        { value: 'supplies_section', label: '用品区' },
        { value: 'grooming_products', label: '洗护/医疗保健品区' },
        { value: 'featured_products', label: '重点品牌/特色产品' },
        { value: 'price_tags', label: '价格标签/促销信息' },
        { value: 'checkout_area', label: '收银台区域' },
      ]
    },
    grooming: {
      name: '洗护/服务区域',
      categories: [
        { value: 'grooming_overview', label: '洗护区全景' },
        { value: 'grooming_station', label: '单个洗护工位' },
        { value: 'waiting_area', label: '等待区/家长休息区' },
        { value: 'service_menu', label: '价目表/服务项目单' },
        { value: 'grooming_brands', label: '使用的洗护产品品牌' },
      ]
    },
    other: {
      name: '其他特色',
      categories: [
        { value: 'brand_elements', label: '品牌元素/文化墙' },
        { value: 'special_facilities', label: '特色服务/设施' },
        { value: 'staff_interaction', label: '员工互动' },
        { value: 'cleanliness', label: '卫生清洁状况' },
        { value: 'other', label: '其他' },
      ]
    }
  };

  useEffect(() => {
    fetchStoreData();
  }, [id]);

  const fetchStoreData = async () => {
    setLoading(true);
    try {
      // 获取门店基本信息
      const response = await storeAPI.getById(id);
      const storeData = response.data;
      form.setFieldsValue(storeData);
      
      // 处理主照片
      if (storeData.main_image) {
        setMainPhoto({
          uid: '-1',
          name: 'main-image.jpg',
          status: 'done',
          url: storeData.main_image,
        });
      }
      
      // 获取门店分类图片
      const imagesResponse = await storeImageAPI.getAll({ store: id });
      const images = imagesResponse.data.results;
      
      // 将图片按分类整理
      const categorizedImages = { ...categoryImages };
      
      images.forEach(image => {
        if (image.category && categorizedImages[image.category]) {
          categorizedImages[image.category].push({
            uid: image.id,
            name: `image-${image.id}.jpg`,
            status: 'done',
            url: image.image,
            dbId: image.id // 保存数据库中的ID，以便后续删除操作
          });
        }
      });
      
      setCategoryImages(categorizedImages);
    } catch (error) {
      console.error('Error fetching store data:', error);
      message.error('获取门店信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleLocationSelect = (location) => {
    form.setFieldsValue({
      longitude: location.lng,
      latitude: location.lat,
    });
  };
  
  const handleAddressChange = (addressInfo) => {
    if (addressInfo && addressInfo.formattedAddress) {
      form.setFieldsValue({
        address: addressInfo.formattedAddress
      });
    }
  };
  
  const handleMainPhotoChange = (info) => {
    if (info.file.status === 'removed') {
      setMainPhoto(null);
      return;
    }
    
    setMainPhoto(info.file);
  };
  
  const handleCategoryImageChange = (info, category) => {
    if (info && info.fileList) {
      setCategoryImages({
        ...categoryImages,
        [category]: info.fileList
      });
    }
  };
  
  const handleRemoveImage = async (category, index) => {
    const image = categoryImages[category][index];
    const updatedImages = [...categoryImages[category]];
    updatedImages.splice(index, 1);
    
    // 更新状态
    setCategoryImages({
      ...categoryImages,
      [category]: updatedImages
    });
    
    // 如果图片已经保存到服务器并且有ID，则从服务器上删除
    if (image.dbId) {
      try {
        await storeImageAPI.delete(image.dbId);
      } catch (error) {
        console.error('Error deleting image:', error);
        message.error('删除图片失败');
      }
    }
  };

  const onFinish = async (values) => {
    setSubmitting(true);
    try {
      // 更新门店基本信息
      await storeAPI.update(id, values);
      
      // 处理主照片上传
      if (mainPhoto && mainPhoto.originFileObj) {
        await storeAPI.uploadMainImage(id, mainPhoto.originFileObj);
      }
      
      // 处理分类图片上传
      const newImageUploads = [];
      
      for (const [category, fileList] of Object.entries(categoryImages)) {
        for (let i = 0; i < fileList.length; i++) {
          const file = fileList[i];
          // 只上传新添加的文件（没有dbId且有originFileObj）
          if (!file.dbId && file.originFileObj) {
            const uploadPromise = storeImageAPI.create({
              store: id,
              image: file.originFileObj,
              caption: `${getCategoryLabel(category)} 图片 ${i+1}`,
              category: category,
              order: i
            });
            newImageUploads.push(uploadPromise);
          }
        }
      }
      
      // 并行处理所有新图片上传以提高效率
      if (newImageUploads.length > 0) {
        await Promise.all(newImageUploads);
      }
      
      message.success('门店信息更新成功');
      navigate(`/view-store/${id}`);
    } catch (error) {
      console.error('Error updating store:', error);
      message.error('门店更新失败');
    } finally {
      setSubmitting(false);
    }
  };
  
  // 获取分类的显示标签
  const getCategoryLabel = (categoryValue) => {
    for (const groupKey in categoryGroups) {
      const category = categoryGroups[groupKey].categories.find(c => c.value === categoryValue);
      if (category) {
        return category.label;
      }
    }
    return categoryValue;
  };
  
  // 获取分类所属的组标签
  const getCategoryGroupName = (categoryValue) => {
    for (const groupKey in categoryGroups) {
      const category = categoryGroups[groupKey].categories.find(c => c.value === categoryValue);
      if (category) {
        return categoryGroups[groupKey].name;
      }
    }
    return '';
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传图片</div>
    </div>
  );
  
  // 所有图片类别的下拉选项
  const allCategoryOptions = () => {
    const options = [];
    
    for (const groupKey in categoryGroups) {
      const group = categoryGroups[groupKey];
      options.push(
        <Select.OptGroup label={group.name} key={groupKey}>
          {group.categories.map(category => (
            <Option value={category.value} key={category.value}>
              {category.label}
            </Option>
          ))}
        </Select.OptGroup>
      );
    }
    
    return options;
  };
  
  // 计算已上传图片的总数
  const getTotalImageCount = () => {
    return Object.values(categoryImages).reduce((total, images) => total + images.length, 0);
  };
  
  // 渲染已上传图片的预览区域
  const renderImageGallery = () => {
    // 获取所有已上传图片的类别
    const categoriesWithImages = Object.keys(categoryImages).filter(
      category => categoryImages[category].length > 0
    );
    
    if (categoriesWithImages.length === 0) {
      return (
        <Empty description="暂无已上传图片" imageStyle={{ height: 60 }} />
      );
    }
    
    // 按照分组组织图片
    const groupedCategories = {};
    
    categoriesWithImages.forEach(category => {
      let groupName = '';
      
      for (const groupKey in categoryGroups) {
        if (categoryGroups[groupKey].categories.some(c => c.value === category)) {
          groupName = categoryGroups[groupKey].name;
          break;
        }
      }
      
      if (!groupedCategories[groupName]) {
        groupedCategories[groupName] = [];
      }
      
      groupedCategories[groupName].push(category);
    });
    
    return (
      <>
        {Object.keys(groupedCategories).map(groupName => (
          <div key={groupName} style={{ marginBottom: 24 }}>
            <Title level={5} style={{ marginBottom: 16 }}>{groupName}</Title>
            
            {groupedCategories[groupName].map(category => (
              <div key={category} style={{ marginBottom: 16 }}>
                <Text strong>{getCategoryLabel(category)}</Text> 
                <Text type="secondary">（{categoryImages[category].length}张）</Text>
                
                <Row gutter={[8, 8]} style={{ marginTop: 8 }}>
                  {categoryImages[category].map((image, index) => (
                    <Col span={6} key={index}>
                      <div style={{ position: 'relative' }}>
                        <img 
                          src={image.url || URL.createObjectURL(image.originFileObj)} 
                          alt={`${getCategoryLabel(category)} ${index + 1}`}
                          style={{ 
                            width: '100%', 
                            height: 120, 
                            objectFit: 'cover',
                            borderRadius: 4
                          }}
                        />
                        <Button 
                          type="text" 
                          danger
                          icon={<DeleteOutlined />} 
                          style={{ 
                            position: 'absolute', 
                            top: 0, 
                            right: 0,
                            background: 'rgba(255, 255, 255, 0.7)'
                          }}
                          onClick={() => handleRemoveImage(category, index)}
                        />
                      </div>
                    </Col>
                  ))}
                </Row>
              </div>
            ))}
          </div>
        ))}
      </>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p>加载门店信息中...</p>
      </div>
    );
  }

  return (
    <div>
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={() => navigate(`/view-store/${id}`)}>返回</Button>
            <Title level={4} style={{ margin: 0 }}>编辑竞对门店</Title>
          </Space>
          {activeTab === '1' && (
            <Button 
              type="primary" 
              icon={<SaveOutlined />}
              onClick={() => form.submit()} 
              loading={submitting}
            >
              保存
            </Button>
          )}
        </div>
      </Card>
      
      <Divider style={{ margin: '16px 0' }} />
      
      <Alert
        message="提示：每个标签页的信息需单独保存"
        description="请在填写完每个标签页的内容后，点击相应标签页内的「保存」按钮进行保存。基本信息页面的保存按钮在右上角。"
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: '16px' }}
      />
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="基本信息" key="1">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            scrollToFirstError
          >
            <Card>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="name"
                    label="门店名称"
                    rules={[{ required: true, message: '请输入门店名称' }]}
                  >
                    <Input placeholder="请输入门店全称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="brand_name"
                    label="门店简称/品牌名"
                  >
                    <Input placeholder="请输入门店简称或品牌名" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="store_type"
                    label="门店类型"
                    rules={[{ required: true, message: '请选择门店类型' }]}
                  >
                    <Select placeholder="请选择门店类型">
                      <Select.Option value="direct_chain">直营连锁</Select.Option>
                      <Select.Option value="franchise_chain">加盟连锁</Select.Option>
                      <Select.Option value="single_store">单体店</Select.Option>
                      <Select.Option value="pet_hospital">宠物医院附带</Select.Option>
                      <Select.Option value="retail_only">纯零售店</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="main_business"
                    label="主营业务"
                    rules={[{ required: false, message: '请选择主营业务' }]}
                  >
                    <Select mode="multiple" placeholder="请选择门店主营业务（可多选）">
                      <Select.Option value="pet_grooming">宠物洗护</Select.Option>
                      <Select.Option value="pet_beauty">宠物美容</Select.Option>
                      <Select.Option value="pet_sale">活体销售</Select.Option>
                      <Select.Option value="exotic_pet_sale">异宠销售</Select.Option>
                      <Select.Option value="pet_supplies">宠物用品</Select.Option>
                      <Select.Option value="pet_fresh_food">宠物鲜食</Select.Option>
                      <Select.Option value="pet_training">宠物训练</Select.Option>
                      <Select.Option value="pet_diagnosis">宠物诊疗</Select.Option>
                      <Select.Option value="pet_funeral">宠物殡葬</Select.Option>
                      <Select.Option value="pet_cafe">宠物咖啡</Select.Option>
                      <Select.Option value="pet_playground">宠物乐园</Select.Option>
                      <Select.Option value="pet_event_planning">宠物活动策划</Select.Option>
                      <Select.Option value="pet_boarding">宠物寄养</Select.Option>
                      <Select.Option value="pet_photography">宠物摄影</Select.Option>
                      <Select.Option value="pet_shipping">宠物托运</Select.Option>
                      <Select.Option value="pet_rental">宠物租赁</Select.Option>
                      <Select.Option value="talent_training">人才培训</Select.Option>
                      <Select.Option value="pet_club">宠物俱乐部</Select.Option>
                      <Select.Option value="comprehensive">综合服务</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="competition_relation"
                    label="竞争关系"
                    rules={[{ required: true, message: '请选择竞争关系' }]}
                  >
                    <Select placeholder="请选择与贵司的竞争关系">
                      <Select.Option value="direct_competitor">直接竞争对手</Select.Option>
                      <Select.Option value="indirect_hospital">间接竞争对手-宠物医院</Select.Option>
                      <Select.Option value="indirect_retail">间接竞争对手-纯零售</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="business_district" label="所属商圈/区域">
                    <Input placeholder="请输入所属商圈或区域" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item
                name="address"
                label="详细地址"
                rules={[{ required: true, message: '请输入详细地址' }]}
              >
                <TextArea
                  placeholder="请输入详细地址，或在下方地图中选择位置自动填充"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="province" label="省份">
                    <Input placeholder="省份" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="city" label="城市">
                    <Input placeholder="城市" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="district" label="区县">
                    <Input placeholder="区县" />
                  </Form.Item>
                </Col>
              </Row>
              
              <AMapLocationPicker 
                onLocationSelect={handleLocationSelect}
                onAddressChange={handleAddressChange}
                initialLocation={{ 
                  lng: form.getFieldValue('longitude') || 116.397428, 
                  lat: form.getFieldValue('latitude') || 39.90923 
                }}
                initialAddress={form.getFieldValue('address')}
                key={`edit-store-map-${id}`}
              />
              
              {/* 隐藏的经纬度字段 */}
              <Row gutter={16} style={{ display: 'none' }}>
                <Col span={12}>
                  <Form.Item name="longitude" label="经度">
                    <InputNumber style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item name="latitude" label="纬度">
                    <InputNumber style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="visibility" label="可见性与易达性">
                    <Select placeholder="请选择门店可见性与易达性">
                      <Select.Option value="excellent">极佳</Select.Option>
                      <Select.Option value="good">良好</Select.Option>
                      <Select.Option value="average">一般</Select.Option>
                      <Select.Option value="poor">较差</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item name="transportation_convenience" label="交通便利性">
                <TextArea
                  placeholder="请描述周边交通情况，如地铁、公交、停车场等"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
              
              <Form.Item name="surrounding_community_type" label="周边社区类型">
                <TextArea
                  placeholder="描述周边社区类型，如高端住宅、普通小区、商住混合、学校附近等"
                  autoSize={{ minRows: 2, maxRows: 4 }}
                />
              </Form.Item>
              
              <Row gutter={16}>
                <Col span={16}>
                  <Form.Item name="phone" label="联系电话">
                    <Input placeholder="多个电话号码用分号(;)分隔" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="wechat_qr" label="微信客服号/群二维码">
                    <Input placeholder="微信客服号或群二维码链接" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item name="business_hours_weekday" label="营业时间（工作日）">
                    <Input placeholder="如: 09:00 - 21:00" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="business_hours_weekend" label="营业时间（周末/节假日）">
                    <Input placeholder="如: 09:00 - 21:00" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item name="opening_year" label="开业年份">
                    <InputNumber style={{ width: '100%' }} placeholder="门店开业年份" />
                  </Form.Item>
                </Col>
              </Row>
              
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item name="store_area" label="门店面积 (m²)">
                    <InputNumber style={{ width: '100%' }} placeholder="门店面积，单位平方米" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="门店主照片">
                    <Upload
                      listType="picture-card"
                      fileList={mainPhoto ? [mainPhoto] : []}
                      beforeUpload={() => false} // Prevent auto upload
                      onChange={handleMainPhotoChange}
                      maxCount={1}
                    >
                      {mainPhoto ? null : uploadButton}
                    </Upload>
                    <div style={{ marginTop: 8 }}>
                      <small>请上传一张主照片，将显示在门店列表中</small>
                    </div>
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item name="notes" label="备注">
                <TextArea
                  placeholder="其它需要备注的信息"
                  autoSize={{ minRows: 2, maxRows: 6 }}
                />
              </Form.Item>
              
              {/* 统一图片管理区域 */}
              <Divider orientation="left">图片资料库</Divider>
              
              <Row gutter={16}>
                <Col span={24}>
                  <Card title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span>竞对门店图片资料 （已上传：{getTotalImageCount()}张）</span>
                    </div>
                  }>
                    <Row gutter={16} style={{ marginBottom: 24 }}>
                      <Col span={16}>
                        <Select
                          style={{ width: '100%' }}
                          placeholder="请选择图片类别"
                          value={currentCategory}
                          onChange={setCurrentCategory}
                        >
                          {allCategoryOptions()}
                        </Select>
                      </Col>
                      <Col span={8}>
                        <Upload
                          listType="text"
                          fileList={[]}
                          beforeUpload={() => false}
                          onChange={(info) => {
                            if (currentCategory) {
                              handleCategoryImageChange({
                                fileList: [
                                  ...categoryImages[currentCategory],
                                  ...info.fileList
                                ]
                              }, currentCategory);
                            } else {
                              message.warning('请先选择图片类别');
                            }
                          }}
                          showUploadList={false}
                          multiple
                          disabled={!currentCategory}
                        >
                          <Button icon={<UploadOutlined />} disabled={!currentCategory}>
                            选择并上传图片
                          </Button>
                        </Upload>
                      </Col>
                    </Row>
                    
                    <div style={{ maxHeight: '600px', overflowY: 'auto', padding: '0 8px' }}>
                      {renderImageGallery()}
                    </div>
                  </Card>
                </Col>
              </Row>
            </Card>
            
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Space>
                <Button onClick={() => navigate(`/view-store/${id}`)}>取消</Button>
                <Button type="primary" htmlType="submit" loading={submitting}>
                  保存
                </Button>
              </Space>
            </div>
          </Form>
        </TabPane>
        
        <TabPane tab="服务项目" key="2">
          <Card>
            <ServiceManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="零售产品" key="3">
          <Card>
            <ProductManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="价格与会员体系" key="4">
          <Card>
            <PricingManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="门店环境与设施" key="5">
          <Card>
            <EnvironmentManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="营销与口碑" key="6">
          <Card>
            <MarketingManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="人员与运营" key="7">
          <Card>
            <StaffOperationsManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="综合评分" key="8">
          <Card>
            <RatingManager storeId={id} />
          </Card>
        </TabPane>
        
        <TabPane tab="活动追踪" key="9">
          <Card>
            <ActivityManager storeId={id} />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default EditStore; 