import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Spin, Select, Typography, Divider, Table, Tag } from 'antd';
import { storeAPI } from '../api/api';

const { Title, Text } = Typography;
const { Option } = Select;

const Statistics = () => {
  const [loading, setLoading] = useState(true);
  const [stores, setStores] = useState([]);
  const [storesByType, setStoresByType] = useState({});
  const [storesByRelation, setStoresByRelation] = useState({});
  const [storesByDistrict, setStoresByDistrict] = useState({});
  const [selectedDistrict, setSelectedDistrict] = useState('all');
  const [districts, setDistricts] = useState([]);

  useEffect(() => {
    fetchStores();
  }, []);

  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await storeAPI.getAll({ limit: 100 });
      const storesData = response.data.results;
      setStores(storesData);
      
      // Process data for statistics
      processData(storesData);
    } catch (error) {
      console.error('Error fetching stores:', error);
    } finally {
      setLoading(false);
    }
  };

  const processData = (storesData) => {
    // Group by store type
    const byType = {};
    storesData.forEach(store => {
      const type = store.store_type;
      if (!byType[type]) {
        byType[type] = [];
      }
      byType[type].push(store);
    });
    setStoresByType(byType);

    // Group by competition relation
    const byRelation = {};
    storesData.forEach(store => {
      const relation = store.competition_relation;
      if (!byRelation[relation]) {
        byRelation[relation] = [];
      }
      byRelation[relation].push(store);
    });
    setStoresByRelation(byRelation);

    // Group by business district
    const byDistrict = {};
    const uniqueDistricts = new Set();
    storesData.forEach(store => {
      const district = store.business_district || '未知商圈';
      uniqueDistricts.add(district);
      if (!byDistrict[district]) {
        byDistrict[district] = [];
      }
      byDistrict[district].push(store);
    });
    setStoresByDistrict(byDistrict);
    setDistricts(Array.from(uniqueDistricts));
  };

  const renderStoreType = (type) => {
    const typeMap = {
      direct_chain: '直营连锁',
      franchise_chain: '加盟连锁',
      single_store: '单体店',
      pet_hospital: '宠物医院附带',
      retail_only: '纯零售店',
    };
    return typeMap[type] || type;
  };

  const renderCompetitionRelation = (relation) => {
    const relationMap = {
      direct_competitor: '直接竞争对手',
      indirect_hospital: '间接竞争对手-宠物医院',
      indirect_retail: '间接竞争对手-纯零售',
    };
    return relationMap[relation] || relation;
  };

  const getTypeColor = (type) => {
    const colorMap = {
      direct_chain: 'blue',
      franchise_chain: 'cyan',
      single_store: 'green',
      pet_hospital: 'purple',
      retail_only: 'orange',
    };
    return colorMap[type] || 'default';
  };

  const getRelationColor = (relation) => {
    const colorMap = {
      direct_competitor: 'red',
      indirect_hospital: 'orange',
      indirect_retail: 'yellow',
    };
    return colorMap[relation] || 'default';
  };

  const filteredStores = selectedDistrict === 'all' 
    ? stores 
    : storesByDistrict[selectedDistrict] || [];

  const columns = [
    {
      title: '门店名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '品牌名',
      dataIndex: 'brand_name',
      key: 'brand_name',
      render: (text) => text || '-',
    },
    {
      title: '门店类型',
      dataIndex: 'store_type',
      key: 'store_type',
      render: (type) => (
        <Tag color={getTypeColor(type)}>{renderStoreType(type)}</Tag>
      ),
    },
    {
      title: '竞争关系',
      dataIndex: 'competition_relation',
      key: 'competition_relation',
      render: (relation) => (
        <Tag color={getRelationColor(relation)}>{renderCompetitionRelation(relation)}</Tag>
      ),
    },
    {
      title: '商圈',
      dataIndex: 'business_district',
      key: 'business_district',
      render: (text) => text || '未知',
    },
    {
      title: '开业年份',
      dataIndex: 'opening_year',
      key: 'opening_year',
      render: (year) => year || '-',
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p>加载统计数据中...</p>
      </div>
    );
  }

  return (
    <div>
      <Title level={4}>竞争对手数据统计</Title>
      <Divider />
      
      <Row gutter={[16, 16]}>
        <Col span={8}>
          <Card title="门店类型分布">
            {Object.keys(storesByType).map(type => (
              <div key={type} style={{ marginBottom: 8 }}>
                <Tag color={getTypeColor(type)} style={{ marginRight: 8 }}>
                  {renderStoreType(type)}
                </Tag>
                <Text>{storesByType[type].length} 家</Text>
              </div>
            ))}
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="竞争关系分布">
            {Object.keys(storesByRelation).map(relation => (
              <div key={relation} style={{ marginBottom: 8 }}>
                <Tag color={getRelationColor(relation)} style={{ marginRight: 8 }}>
                  {renderCompetitionRelation(relation)}
                </Tag>
                <Text>{storesByRelation[relation].length} 家</Text>
              </div>
            ))}
          </Card>
        </Col>
        
        <Col span={8}>
          <Card title="商圈分布">
            {Object.keys(storesByDistrict).map(district => (
              <div key={district} style={{ marginBottom: 8 }}>
                <Text strong style={{ marginRight: 8 }}>{district}:</Text>
                <Text>{storesByDistrict[district].length} 家</Text>
              </div>
            ))}
          </Card>
        </Col>
      </Row>
      
      <Divider />
      
      <Card title="门店列表筛选" style={{ marginTop: 16 }}>
        <div style={{ marginBottom: 16 }}>
          <Text strong style={{ marginRight: 8 }}>按商圈筛选:</Text>
          <Select 
            style={{ width: 200 }} 
            value={selectedDistrict}
            onChange={value => setSelectedDistrict(value)}
          >
            <Option value="all">全部商圈</Option>
            {districts.map(district => (
              <Option key={district} value={district}>{district}</Option>
            ))}
          </Select>
        </div>
        
        <Table 
          dataSource={filteredStores} 
          columns={columns} 
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      <Card title="数据分析" style={{ marginTop: 16 }}>
        <Text>更多高级数据分析功能正在开发中...</Text>
      </Card>
    </div>
  );
};

export default Statistics; 