import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Space, Card, Divider, message, Row, Col, InputNumber, Rate } from 'antd';
import { marketingAPI } from '../api/api';

const { TextArea } = Input;
const { Option } = Select;

const MarketingForm = ({ storeId, marketingId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (marketingId) {
      setIsEdit(true);
      fetchMarketingData();
    } else {
      form.setFieldsValue({ store: storeId });
    }
  }, [storeId, marketingId, form]);

  const fetchMarketingData = async () => {
    setLoading(true);
    try {
      const response = await marketingAPI.getById(marketingId);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('Error fetching marketing data:', error);
      message.error('获取营销与口碑信息失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await marketingAPI.update(marketingId, values);
        message.success('营销与口碑信息更新成功');
      } else {
        await marketingAPI.create(values);
        message.success('营销与口碑信息添加成功');
        form.resetFields();
        form.setFieldsValue({ store: storeId });
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving marketing data:', error);
      message.error(isEdit ? '营销与口碑信息更新失败' : '营销与口碑信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          promotion_frequency: 'average',
          social_media_presence: 'moderate'
        }}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="promotion_frequency"
              label="促销活动频率"
            >
              <Select placeholder="请选择促销活动频率">
                <Option value="frequent">频繁（每周都有）</Option>
                <Option value="regular">定期（每月有）</Option>
                <Option value="average">一般（偶尔有）</Option>
                <Option value="rare">很少（几乎没有）</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="social_media_presence"
              label="社交媒体存在感"
            >
              <Select placeholder="请选择社交媒体存在感">
                <Option value="strong">强（多平台活跃）</Option>
                <Option value="moderate">中等（有活跃但不多）</Option>
                <Option value="weak">弱（几乎不活跃）</Option>
                <Option value="none">无（没有社交媒体）</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="current_promotions"
          label="当前促销活动描述"
        >
          <TextArea
            placeholder="请描述当前正在进行的促销活动，如折扣、满减、买赠等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="marketing_methods"
          label="主要营销手段"
        >
          <TextArea
            placeholder="请描述该店铺使用的主要营销手段，如线上广告、线下传单、会员营销等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="social_media_accounts"
          label="社交媒体账号"
        >
          <TextArea
            placeholder="请列出该店铺的社交媒体账号，如微信公众号、微博、小红书等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="social_media_content"
          label="社交媒体内容特点"
        >
          <TextArea
            placeholder="请描述该店铺社交媒体内容的特点，如更新频率、内容风格、互动情况等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="customer_rating"
              label="顾客评价综合评分 (1-5分)"
            >
              <Rate allowHalf />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              name="review_count"
              label="评论数量"
            >
              <InputNumber
                min={0}
                placeholder="店铺在各平台的评论总数"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              name="positive_rate"
              label="好评率 (%)"
            >
              <InputNumber
                min={0}
                max={100}
                placeholder="好评率百分比"
                style={{ width: '100%' }}
                formatter={value => `${value}%`}
                parser={value => value.replace('%', '')}
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="customer_feedback_summary"
          label="顾客反馈概要"
        >
          <TextArea
            placeholder="请总结顾客反馈的主要内容，包括优点和不足"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>
        
        <Form.Item
          name="marketing_strategy_notes"
          label="营销策略评价"
        >
          <TextArea
            placeholder="请评价该店铺的整体营销策略，如营销方向、目标客户群体、营销效果等"
            autoSize={{ minRows: 3, maxRows: 6 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新营销信息' : '添加营销信息'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default MarketingForm; 