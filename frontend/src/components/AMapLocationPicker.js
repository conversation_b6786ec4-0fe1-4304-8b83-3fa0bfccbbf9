import React, { useState, useEffect, useLayoutEffect, useRef } from 'react';
import { Card, Input, Button, message, Spin } from 'antd';
import { 
  loadAMapAPI, 
  initMapInstance,
  destroyMapInstance,
  DEFAULT_CENTER, 
  DEFAULT_ZOOM 
} from '../config/mapConfig';

const { Search } = Input;

// 固定容器ID前缀，但根据组件实例生成唯一ID
const MAP_CONTAINER_ID_PREFIX = 'map-location-picker-container';

const AMapLocationPicker = ({ onLocationSelect, onAddressChange, initialLocation, initialAddress, readOnly = false }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const mapInstanceRef = useRef(null);
  const markerRef = useRef(null);
  const mapContainerRef = useRef(null);
  const containerIdRef = useRef(`${MAP_CONTAINER_ID_PREFIX}-${Math.random().toString(36).substring(2, 9)}`);
  const containerCreatedRef = useRef(false);
  
  // 反向地理编码：根据坐标获取地址
  const getAddressByLocation = async (location) => {
    if (!location) return;
    
    try {
      const AMap = await loadAMapAPI();
      const geocoder = new AMap.Geocoder();
      
      return new Promise((resolve, reject) => {
        geocoder.getAddress([location.lng, location.lat], (status, result) => {
          if (status === 'complete' && result.info === 'OK') {
            const addressInfo = {
              formattedAddress: result.regeocode.formattedAddress,
              province: result.regeocode.addressComponent.province,
              city: result.regeocode.addressComponent.city,
              district: result.regeocode.addressComponent.district,
            };
            
            if (onAddressChange) {
              onAddressChange(addressInfo);
            }
            resolve(addressInfo);
          } else {
            reject(new Error('Failed to get address'));
          }
        });
      });
    } catch (error) {
      console.error('Reverse geocoding error:', error);
      throw error;
    }
  };

  // 创建DOM容器
  const createMapContainer = () => {
    // 检查是否已创建容器
    if (containerCreatedRef.current) return;
    
    // 检查容器是否已存在
    const existingContainer = document.getElementById(containerIdRef.current);
    if (existingContainer) {
      mapContainerRef.current = existingContainer;
      containerCreatedRef.current = true;
      return;
    }
    
    // 创建新的容器
    const mapContainer = document.createElement('div');
    mapContainer.id = containerIdRef.current;
    mapContainer.style.height = '100%';
    mapContainer.style.width = '100%';
    
    // 将容器添加到父元素
    const parentEl = document.getElementById('map-container-wrapper');
    if (parentEl) {
      // 清空父元素
      while (parentEl.firstChild) {
        parentEl.removeChild(parentEl.firstChild);
      }
      parentEl.appendChild(mapContainer);
      mapContainerRef.current = mapContainer;
      containerCreatedRef.current = true;
    }
  };

  // 初始化地图
  const initMap = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 创建DOM容器
      createMapContainer();
      
      // 确保DOM容器已经存在
      if (!mapContainerRef.current) {
        setTimeout(initMap, 100);
        return;
      }
      
      // 选择初始位置
      const initialCenter = initialLocation ? 
        [initialLocation.lng, initialLocation.lat] : 
        DEFAULT_CENTER;
      
      // 使用增强的地图初始化方法
      const { map: mapInstance, AMap } = await initMapInstance(containerIdRef.current, {
        zoom: 12,
        center: initialCenter,
      });
      
      // 保存地图实例引用
      mapInstanceRef.current = mapInstance;
      
      // 添加工具栏控件
      mapInstance.addControl(new AMap.ToolBar());
      
      // 如果不是只读模式，添加定位控件
      if (!readOnly) {
        const geolocation = new AMap.Geolocation({
          enableHighAccuracy: true,
          timeout: 10000,
          position: 'RB',
          offset: [10, 20],
        });
        mapInstance.addControl(geolocation);
      }
      
      // 创建标记
      const markerInstance = new AMap.Marker({
        position: initialLocation ? [initialLocation.lng, initialLocation.lat] : null,
        draggable: !readOnly,
        cursor: readOnly ? 'default' : 'move',
        animation: 'AMAP_ANIMATION_DROP',
      });
      
      markerRef.current = markerInstance;
      
      if (initialLocation) {
        markerInstance.setMap(mapInstance);
        if (!initialAddress && onAddressChange) {
          getAddressByLocation(initialLocation).catch(err => {
            console.error('Error getting address for initial location:', err);
          });
        }
      }
      
      // 添加交互事件
      if (!readOnly) {
        // 添加地图点击事件
        mapInstance.on('click', (e) => {
          const { lng, lat } = e.lnglat;
          markerInstance.setPosition([lng, lat]);
          if (!markerInstance.getMap()) {
            markerInstance.setMap(mapInstance);
          }
          
          const location = { lng, lat };
          if (onLocationSelect) {
            onLocationSelect(location);
          }
          getAddressByLocation(location).catch(err => {
            console.error('Error getting address after map click:', err);
          });
        });
        
        // 添加标记拖拽事件
        markerInstance.on('dragend', (e) => {
          const { lng, lat } = e.lnglat;
          const location = { lng, lat };
          if (onLocationSelect) {
            onLocationSelect(location);
          }
          getAddressByLocation(location).catch(err => {
            console.error('Error getting address after marker drag:', err);
          });
        });
      }
      
      setLoading(false);
    } catch (error) {
      console.error('Failed to initialize map:', error);
      setError('地图初始化失败，请刷新重试');
      setLoading(false);
    }
  };
  
  // 组件挂载时创建DOM容器
  useEffect(() => {
    return () => {
      // 组件卸载时清理地图
      
      // 如果标记存在，先移除标记
      if (markerRef.current && markerRef.current.getMap()) {
        try {
          markerRef.current.setMap(null);
        } catch (e) {
          console.error('Error removing marker:', e);
        }
      }
      
      // 使用中央管理系统销毁地图实例
      if (containerIdRef.current) {
        destroyMapInstance(containerIdRef.current);
      }
      
      mapInstanceRef.current = null;
      markerRef.current = null;
      
      // 最后一步：谨慎处理容器DOM
      try {
        const container = document.getElementById(containerIdRef.current);
        if (container && container.parentNode) {
          // 使用父节点的removeChild方法，避免React的DOM管理冲突
          container.parentNode.removeChild(container);
        }
      } catch (e) {
        console.error('Error removing map container:', e);
      }
      
      containerCreatedRef.current = false;
    };
  }, []);
  
  // 属性变更时使用useEffect重新初始化地图，避免DOM同步问题
  useEffect(() => {
    // 使用延时来确保DOM已更新
    const timer = setTimeout(() => {
      initMap();
    }, 150);
    
    return () => {
      clearTimeout(timer);
    };
  }, [initialLocation, readOnly]);

  // 处理搜索
  const handleSearch = async () => {
    if (!searchValue || !mapInstanceRef.current || !markerRef.current) return;

    try {
      const AMap = await loadAMapAPI();
      
      const placeSearch = new AMap.PlaceSearch({
        city: '全国',
      });

      placeSearch.search(searchValue, (status, result) => {
        if (status === 'complete' && result.info === 'OK') {
          const poi = result.poiList.pois[0];
          const position = [poi.location.lng, poi.location.lat];
          
          if (mapInstanceRef.current) {
            mapInstanceRef.current.setCenter(position);
          }
          
          if (markerRef.current) {
            markerRef.current.setPosition(position);
            if (!markerRef.current.getMap() && mapInstanceRef.current) {
              markerRef.current.setMap(mapInstanceRef.current);
            }
            
            const location = { lng: poi.location.lng, lat: poi.location.lat };
            if (onLocationSelect) {
              onLocationSelect(location);
            }
            getAddressByLocation(location).catch(err => {
              console.error('Error getting address after search:', err);
            });
            
            message.success('位置已找到');
          } else {
            message.warning('标记点不可用，请刷新重试');
          }
        } else {
          message.error('找不到该地址，请尝试其他关键词');
        }
      });
    } catch (error) {
      console.error('Search error:', error);
      message.error('搜索失败，请稍后再试');
    }
  };
  
  // 获取当前位置
  const getCurrentLocation = async () => {
    if (!mapInstanceRef.current || !markerRef.current) {
      message.error('地图尚未初始化，请刷新页面重试');
      return;
    }
    
    try {
      const AMap = await loadAMapAPI();
      
      const geolocation = new AMap.Geolocation({
        enableHighAccuracy: true,
        timeout: 10000,
      });
      
      geolocation.getCurrentPosition((status, result) => {
        if (status === 'complete' && mapInstanceRef.current && markerRef.current) {
          const position = [result.position.lng, result.position.lat];
          
          mapInstanceRef.current.setCenter(position);
          markerRef.current.setPosition(position);
          if (!markerRef.current.getMap()) {
            markerRef.current.setMap(mapInstanceRef.current);
          }
          
          const location = { lng: result.position.lng, lat: result.position.lat };
          if (onLocationSelect) {
            onLocationSelect(location);
          }
          getAddressByLocation(location).catch(err => {
            console.error('Error getting address after geolocation:', err);
          });
          
          message.success('已定位到当前位置');
        } else {
          message.error('获取当前位置失败: ' + result.message);
        }
      });
    } catch (error) {
      console.error('Geolocation error:', error);
      message.error('获取位置失败，请稍后再试');
    }
  };
  
  // 重新加载地图
  const reloadMap = () => {
    if (containerIdRef.current) {
      destroyMapInstance(containerIdRef.current);
    }
    mapInstanceRef.current = null;
    containerCreatedRef.current = false;
    
    // 使用延时等待DOM更新
    setTimeout(() => {
      initMap();
    }, 150);
  };

  // 渲染地图内容
  const renderMapContent = () => {
    if (error) {
      return (
        <div style={{ height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', gap: '16px' }}>
          <div style={{ color: 'red' }}>{error}</div>
          <Button type="primary" onClick={reloadMap}>重新加载地图</Button>
        </div>
      );
    }
    
    return (
      <div style={{ position: 'relative', height: '300px', width: '100%' }}>
        <div id="map-container-wrapper" style={{ height: '100%', width: '100%' }}></div>
        {loading && (
          <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', background: 'rgba(255,255,255,0.7)', zIndex: 10 }}>
            <Spin tip="地图加载中..." />
          </div>
        )}
      </div>
    );
  };

  return (
    <div style={{ marginBottom: 16 }}>
      <Card 
        title="地址定位" 
        bordered={false} 
        style={{ marginBottom: 16 }}
        extra={
          <Button size="small" onClick={reloadMap} disabled={loading}>
            {loading ? '加载中...' : '重新加载地图'}
          </Button>
        }
      >
        {!readOnly && (
          <div style={{ marginBottom: 16, display: 'flex', gap: '8px' }}>
            <Search
              placeholder="输入地址关键词搜索"
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              onSearch={handleSearch}
              enterButton
              style={{ flex: 1 }}
              disabled={loading || !!error}
            />
            <Button onClick={getCurrentLocation} disabled={loading || !!error}>
              定位当前位置
            </Button>
          </div>
        )}
        
        {renderMapContent()}
        
        {!loading && !error && !readOnly && (
          <div style={{ fontSize: '12px', color: '#888', marginTop: 8 }}>
            点击地图选择位置，或拖动标记调整位置
          </div>
        )}
      </Card>
    </div>
  );
};

export default AMapLocationPicker;