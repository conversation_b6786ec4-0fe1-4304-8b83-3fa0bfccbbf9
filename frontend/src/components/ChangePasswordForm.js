import React, { useState } from 'react';
import { Form, Input, Button, message, Card } from 'antd';
import { LockOutlined } from '@ant-design/icons';
import axios from 'axios';
import { USER_API } from '../config/api.config';

const ChangePasswordForm = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 提交表单
  const onFinish = async (values) => {
    if (values.newPassword !== values.confirmPassword) {
      message.error('两次输入的新密码不一致');
      return;
    }

    setLoading(true);
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        message.error('请先登录');
        return;
      }

      const response = await axios.post(
        USER_API.CHANGE_OWN_PASSWORD,
        {
          old_password: values.oldPassword,
          new_password: values.newPassword
        },
        {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        }
      );

      message.success('密码已成功修改');
      form.resetFields();
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error(error.response?.data?.error || '修改密码失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card title="修改密码" bordered={false} style={{ maxWidth: 500, margin: '0 auto' }}>
      <Form
        form={form}
        name="change_password"
        layout="vertical"
        onFinish={onFinish}
      >
        <Form.Item
          name="oldPassword"
          label="当前密码"
          rules={[
            {
              required: true,
              message: '请输入当前密码',
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入当前密码"
          />
        </Form.Item>

        <Form.Item
          name="newPassword"
          label="新密码"
          rules={[
            {
              required: true,
              message: '请输入新密码',
            },
            {
              min: 8,
              message: '密码长度至少为8个字符',
            },
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请输入新密码"
          />
        </Form.Item>

        <Form.Item
          name="confirmPassword"
          label="确认新密码"
          rules={[
            {
              required: true,
              message: '请确认新密码',
            },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue('newPassword') === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error('两次输入的密码不一致'));
              },
            }),
          ]}
        >
          <Input.Password
            prefix={<LockOutlined />}
            placeholder="请再次输入新密码"
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            block
          >
            修改密码
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ChangePasswordForm; 