import React, { useState, useEffect } from 'react';
import { Upload, Button, Select, Input, Form, Card, Row, Col, Divider, Tabs, message, Modal, Image } from 'antd';
import { PlusOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons';
import { storeImageAPI } from '../api/api';

const { Option } = Select;
const { TabPane } = Tabs;

const CategorizedImageUpload = ({ storeId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [fileList, setFileList] = useState([]);
  const [existingImages, setExistingImages] = useState([]);
  const [activeTab, setActiveTab] = useState('upload');
  
  // Image categories grouped by area
  const categoryGroups = {
    exterior: [
      { value: 'storefront', label: '门店正面/入口' },
      { value: 'window_display', label: '橱窗展示' },
      { value: 'surroundings', label: '周边环境' },
      { value: 'business_info', label: '营业时间/联系方式' },
    ],
    retail: [
      { value: 'retail_overview', label: '零售区整体布局' },
      { value: 'food_section', label: '主粮区' },
      { value: 'snack_section', label: '零食区' },
      { value: 'supplies_section', label: '用品区' },
      { value: 'grooming_products', label: '洗护/医疗保健品区' },
      { value: 'featured_products', label: '重点品牌/特色产品' },
      { value: 'price_tags', label: '价格标签/促销信息' },
      { value: 'checkout_area', label: '收银台区域' },
    ],
    grooming: [
      { value: 'grooming_overview', label: '洗护区全景' },
      { value: 'grooming_station', label: '单个洗护工位' },
      { value: 'waiting_area', label: '等待区/家长休息区' },
      { value: 'service_menu', label: '价目表/服务项目单' },
      { value: 'grooming_brands', label: '使用的洗护产品品牌' },
    ],
    other: [
      { value: 'brand_elements', label: '品牌元素/文化墙' },
      { value: 'special_facilities', label: '特色服务/设施' },
      { value: 'staff_interaction', label: '员工互动' },
      { value: 'cleanliness', label: '卫生清洁状况' },
      { value: 'other', label: '其他' },
    ]
  };
  
  // Flatten all categories for the select component
  const allCategories = [
    ...categoryGroups.exterior,
    ...categoryGroups.retail,
    ...categoryGroups.grooming,
    ...categoryGroups.other
  ];
  
  useEffect(() => {
    if (storeId) {
      fetchExistingImages();
    }
  }, [storeId]);
  
  const fetchExistingImages = async () => {
    try {
      const response = await storeImageAPI.getAll({ store: storeId });
      if (response.data && response.data.results) {
        setExistingImages(response.data.results);
      }
    } catch (error) {
      console.error('Error fetching store images:', error);
      message.error('获取门店图片失败');
    }
  };

  const handleCancel = () => setPreviewVisible(false);

  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }

    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
  };

  const handleChange = ({ fileList }) => setFileList(fileList);

  const handleExistingImagePreview = (image) => {
    setPreviewImage(image.image);
    setPreviewTitle(`${image.category_display || '未分类'} - ${image.caption || '无描述'}`);
    setPreviewVisible(true);
  };

  const handleDeleteExistingImage = async (imageId) => {
    try {
      await storeImageAPI.delete(imageId);
      message.success('图片删除成功');
      fetchExistingImages();
    } catch (error) {
      console.error('Error deleting image:', error);
      message.error('图片删除失败');
    }
  };

  const onFinish = async (values) => {
    if (fileList.length === 0) {
      message.warning('请至少选择一张图片');
      return;
    }

    setLoading(true);
    try {
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i];
        if (file.originFileObj) {
          await storeImageAPI.create({
            store: storeId,
            image: file.originFileObj,
            caption: values.caption,
            category: values.category,
            order: i
          });
        }
      }
      message.success('图片上传成功');
      setFileList([]);
      form.resetFields();
      fetchExistingImages();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      message.error('图片上传失败');
    } finally {
      setLoading(false);
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传图片</div>
    </div>
  );

  // Function to group existing images by category
  const groupImagesByCategory = () => {
    const grouped = {};
    
    existingImages.forEach(image => {
      const category = image.category || 'other';
      if (!grouped[category]) {
        grouped[category] = [];
      }
      grouped[category].push(image);
    });
    
    return grouped;
  };

  const groupedImages = groupImagesByCategory();

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="上传新图片" key="upload">
          <Card>
            <Form form={form} onFinish={onFinish} layout="vertical">
              <Form.Item
                name="category"
                label="图片类别"
                rules={[{ required: true, message: '请选择图片类别' }]}
              >
                <Select placeholder="请选择图片类别">
                  <Select.OptGroup label="门店外部照片">
                    {categoryGroups.exterior.map(cat => (
                      <Option key={cat.value} value={cat.value}>{cat.label}</Option>
                    ))}
                  </Select.OptGroup>
                  <Select.OptGroup label="零售区域">
                    {categoryGroups.retail.map(cat => (
                      <Option key={cat.value} value={cat.value}>{cat.label}</Option>
                    ))}
                  </Select.OptGroup>
                  <Select.OptGroup label="洗护/服务区域">
                    {categoryGroups.grooming.map(cat => (
                      <Option key={cat.value} value={cat.value}>{cat.label}</Option>
                    ))}
                  </Select.OptGroup>
                  <Select.OptGroup label="其他特色照片">
                    {categoryGroups.other.map(cat => (
                      <Option key={cat.value} value={cat.value}>{cat.label}</Option>
                    ))}
                  </Select.OptGroup>
                </Select>
              </Form.Item>

              <Form.Item
                name="caption"
                label="图片说明"
              >
                <Input placeholder="请输入图片说明描述" />
              </Form.Item>

              <Form.Item label="选择图片">
                <Upload
                  listType="picture-card"
                  fileList={fileList}
                  onPreview={handlePreview}
                  onChange={handleChange}
                  beforeUpload={() => false}
                  multiple
                >
                  {fileList.length >= 10 ? null : uploadButton}
                </Upload>
                <div style={{ marginTop: 8 }}>
                  <small>批量上传相同类别的图片，每次最多上传10张</small>
                </div>
              </Form.Item>

              <Form.Item>
                <Button type="primary" htmlType="submit" loading={loading}>
                  上传图片
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
        
        <TabPane tab={`查看已上传图片 (${existingImages.length})`} key="view">
          <Card>
            <Tabs type="card">
              <TabPane tab="按类别查看" key="by-category">
                {Object.keys(categoryGroups).map(groupKey => (
                  <div key={groupKey}>
                    <Divider orientation="left">
                      {groupKey === 'exterior' ? '门店外部照片' : 
                       groupKey === 'retail' ? '零售区域' :
                       groupKey === 'grooming' ? '洗护/服务区域' : '其他特色照片'}
                    </Divider>
                    <Row gutter={[16, 16]}>
                      {categoryGroups[groupKey].map(category => {
                        const images = groupedImages[category.value] || [];
                        return images.length > 0 ? (
                          <Col span={24} key={category.value}>
                            <Card 
                              size="small" 
                              title={`${category.label} (${images.length})`}
                              style={{ marginBottom: '16px' }}
                            >
                              <Row gutter={[16, 16]}>
                                {images.map(image => (
                                  <Col span={6} key={image.id}>
                                    <Card
                                      hoverable
                                      style={{ marginBottom: '8px' }}
                                      cover={
                                        <img 
                                          alt={image.caption || '无描述'} 
                                          src={image.image}
                                          style={{ height: '150px', objectFit: 'cover' }}
                                        />
                                      }
                                      actions={[
                                        <EyeOutlined key="view" onClick={() => handleExistingImagePreview(image)} />,
                                        <DeleteOutlined key="delete" onClick={() => handleDeleteExistingImage(image.id)} />
                                      ]}
                                    >
                                      <Card.Meta
                                        title={image.caption || '无描述'}
                                        description={`上传时间: ${new Date(image.uploaded_at).toLocaleString()}`}
                                      />
                                    </Card>
                                  </Col>
                                ))}
                              </Row>
                            </Card>
                          </Col>
                        ) : null;
                      })}
                    </Row>
                  </div>
                ))}
              </TabPane>
              
              <TabPane tab="全部图片" key="all">
                <Row gutter={[16, 16]}>
                  {existingImages.map(image => (
                    <Col span={6} key={image.id}>
                      <Card
                        hoverable
                        style={{ marginBottom: '16px' }}
                        cover={
                          <img 
                            alt={image.caption || '无描述'} 
                            src={image.image}
                            style={{ height: '150px', objectFit: 'cover' }}
                          />
                        }
                        actions={[
                          <EyeOutlined key="view" onClick={() => handleExistingImagePreview(image)} />,
                          <DeleteOutlined key="delete" onClick={() => handleDeleteExistingImage(image.id)} />
                        ]}
                      >
                        <Card.Meta
                          title={image.category_display || '未分类'}
                          description={image.caption || '无描述'}
                        />
                      </Card>
                    </Col>
                  ))}
                </Row>
              </TabPane>
            </Tabs>
          </Card>
        </TabPane>
      </Tabs>

      <Modal
        visible={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={handleCancel}
      >
        <img alt="preview" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

// Helper function to get base64 from file
function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result);
    reader.onerror = error => reject(error);
  });
}

export default CategorizedImageUpload; 