import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Space, Card, Divider, message, Row, Col, InputNumber } from 'antd';
import { environmentAPI } from '../api/api';

const { TextArea } = Input;
const { Option } = Select;

const EnvironmentForm = ({ storeId, environmentId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (environmentId) {
      setIsEdit(true);
      fetchEnvironmentData();
    } else {
      form.setFieldsValue({ store: storeId });
    }
  }, [storeId, environmentId, form]);

  const fetchEnvironmentData = async () => {
    setLoading(true);
    try {
      const response = await environmentAPI.getById(environmentId);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('Error fetching environment data:', error);
      message.error('获取门店环境与设施信息失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await environmentAPI.update(environmentId, values);
        message.success('门店环境与设施信息更新成功');
      } else {
        await environmentAPI.create(values);
        message.success('门店环境与设施信息添加成功');
        form.resetFields();
        form.setFieldsValue({ store: storeId });
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving environment data:', error);
      message.error(isEdit ? '门店环境与设施信息更新失败' : '门店环境与设施信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          cleanliness: 7,
          smell: 'light',
          layout_rationality: 7,
          lighting: 'bright',
          ventilation: 'average',
        }}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="cleanliness"
              label="门店清洁度 (1-10分)"
            >
              <InputNumber 
                min={1} 
                max={10} 
                style={{ width: '100%' }} 
                placeholder="请为门店清洁度打分，1-10分"
              />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              name="smell"
              label="店内气味"
            >
              <Select placeholder="请选择店内气味情况">
                <Option value="fresh">清新无异味</Option>
                <Option value="light">轻微宠物味</Option>
                <Option value="moderate">较重宠物味</Option>
                <Option value="unpleasant">难闻</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              name="layout_rationality"
              label="空间布局合理性 (1-10分)"
            >
              <InputNumber 
                min={1} 
                max={10} 
                style={{ width: '100%' }} 
                placeholder="请为空间布局合理性打分，1-10分" 
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="decoration_style"
              label="装修风格"
            >
              <Input placeholder="请描述店铺装修风格，如简约、豪华、日系等" />
            </Form.Item>
          </Col>
          
          <Col span={6}>
            <Form.Item
              name="lighting"
              label="采光情况"
            >
              <Select placeholder="请选择店内采光情况">
                <Option value="very_bright">非常明亮</Option>
                <Option value="bright">明亮</Option>
                <Option value="average">一般</Option>
                <Option value="dim">偏暗</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={6}>
            <Form.Item
              name="ventilation"
              label="通风情况"
            >
              <Select placeholder="请选择店内通风情况">
                <Option value="good">良好</Option>
                <Option value="average">一般</Option>
                <Option value="poor">较差</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="beauty_equipment"
          label="美容区设备情况"
        >
          <TextArea
            placeholder="请描述美容区的设备情况，如有哪些设备、设备品牌、新旧程度等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="retail_display"
          label="零售区货架与陈列"
        >
          <TextArea
            placeholder="请描述零售区的货架与陈列情况，如货架类型、商品陈列方式等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="customer_facilities"
          label="顾客便利设施"
        >
          <TextArea
            placeholder="请描述店内为顾客提供的便利设施，如休息区、饮水机、WIFI等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="waiting_area"
          label="等候区设置"
        >
          <TextArea
            placeholder="请描述等候区的设置情况，如座椅数量、舒适度、娱乐设施等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="store_atmosphere"
          label="店铺整体氛围"
        >
          <TextArea
            placeholder="请描述店铺整体给人的感受和氛围"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="notes"
          label="其他备注"
        >
          <TextArea
            placeholder="其它需要备注的信息"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新环境信息' : '添加环境信息'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default EnvironmentForm; 