import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Select, Space, Card, Divider, message, Row, Col, InputNumber, TimePicker } from 'antd';
import { staffAPI } from '../api/api';

const { TextArea } = Input;
const { Option } = Select;

const StaffOperationsForm = ({ storeId, staffId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);

  useEffect(() => {
    if (staffId) {
      setIsEdit(true);
      fetchStaffData();
    } else {
      form.setFieldsValue({ store: storeId });
    }
  }, [storeId, staffId, form]);

  const fetchStaffData = async () => {
    setLoading(true);
    try {
      const response = await staffAPI.getById(staffId);
      form.setFieldsValue(response.data);
    } catch (error) {
      console.error('Error fetching staff data:', error);
      message.error('获取人员与运营信息失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      if (isEdit) {
        await staffAPI.update(staffId, values);
        message.success('人员与运营信息更新成功');
      } else {
        await staffAPI.create(values);
        message.success('人员与运营信息添加成功');
        form.resetFields();
        form.setFieldsValue({ store: storeId });
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving staff data:', error);
      message.error(isEdit ? '人员与运营信息更新失败' : '人员与运营信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          employee_service_quality: 7,
          groomer_skill_level: 'average',
          staff_attitude: 'good',
        }}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="total_staff"
              label="总员工数"
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="门店总员工数"
              />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              name="groomers"
              label="美容师数量"
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="美容师数量"
              />
            </Form.Item>
          </Col>
          
          <Col span={8}>
            <Form.Item
              name="salespeople"
              label="销售人员数量"
            >
              <InputNumber
                min={0}
                style={{ width: '100%' }}
                placeholder="销售人员数量"
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="staff_attitude"
              label="员工服务态度"
            >
              <Select placeholder="请选择员工服务态度">
                <Option value="excellent">非常好</Option>
                <Option value="good">良好</Option>
                <Option value="average">一般</Option>
                <Option value="poor">较差</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="employee_service_quality"
              label="员工服务质量 (1-10分)"
            >
              <InputNumber
                min={1}
                max={10}
                style={{ width: '100%' }}
                placeholder="请为员工服务质量打分，1-10分"
              />
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="groomer_skill_level"
              label="美容师技术水平"
            >
              <Select placeholder="请选择美容师技术水平">
                <Option value="high">高水平</Option>
                <Option value="average">中等水平</Option>
                <Option value="low">入门水平</Option>
                <Option value="mixed">水平参差不齐</Option>
              </Select>
            </Form.Item>
          </Col>
          
          <Col span={12}>
            <Form.Item
              name="staff_training"
              label="员工培训情况"
            >
              <Select placeholder="请选择员工培训情况">
                <Option value="regular">定期系统培训</Option>
                <Option value="occasional">偶尔培训</Option>
                <Option value="rare">很少培训</Option>
                <Option value="unknown">不清楚</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="staff_turnover"
          label="员工流动性"
        >
          <Select placeholder="请选择员工流动性情况">
            <Option value="high">较高</Option>
            <Option value="moderate">一般</Option>
            <Option value="low">较低</Option>
            <Option value="unknown">不清楚</Option>
          </Select>
        </Form.Item>
        
        <Form.Item
          name="management_structure"
          label="管理架构"
        >
          <TextArea
            placeholder="请描述门店的管理架构，如店长、部门主管等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="operational_efficiency"
          label="运营效率"
        >
          <TextArea
            placeholder="请描述门店的运营效率，如排班、管理、接待流程等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="customer_appointment_system"
          label="顾客预约系统"
        >
          <TextArea
            placeholder="请描述门店的预约系统，如线上预约、电话预约等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="workload_description"
          label="工作量情况"
        >
          <TextArea
            placeholder="请描述门店的工作量情况，如日均接待量、高峰期情况等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="notes"
          label="其他备注"
        >
          <TextArea
            placeholder="其它需要备注的信息"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新人员与运营信息' : '添加人员与运营信息'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default StaffOperationsForm; 