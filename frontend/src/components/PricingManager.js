import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, message, Spin, Descriptions, Divider, Space, Tabs } from 'antd';
import { PlusOutlined, EditOutlined } from '@ant-design/icons';
import { pricingAPI } from '../api/api';
import PricingForm from './PricingForm';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const PricingManager = ({ storeId }) => {
  const [loading, setLoading] = useState(true);
  const [pricing, setPricing] = useState(null);
  const [isEditing, setIsEditing] = useState(false);
  const [activeTab, setActiveTab] = useState('pricing');

  useEffect(() => {
    if (storeId) {
      fetchPricingData();
    }
  }, [storeId]);

  const fetchPricingData = async () => {
    setLoading(true);
    try {
      const response = await pricingAPI.getAll({ store: storeId });
      const pricingData = response.data.results || [];
      
      if (pricingData.length > 0) {
        setPricing(pricingData[0]);
      } else {
        setPricing(null);
      }
    } catch (error) {
      console.error('Error fetching pricing data:', error);
      message.error('获取价格与会员体系信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddPricing = () => {
    setIsEditing(true);
  };

  const handleEditPricing = () => {
    setIsEditing(true);
  };

  const handlePricingSaved = () => {
    setIsEditing(false);
    fetchPricingData();
  };

  const renderPriceLevelText = (level) => {
    const levelMap = {
      high_end: '高端',
      mid_high: '中高端',
      mid_range: '中端',
      mid_low: '中低端',
      economy: '经济型',
    };
    return levelMap[level] || level;
  };

  const renderTransparencyText = (transparency) => {
    const transparencyMap = {
      transparent: '公开透明',
      partially: '部分公开',
      inquiry: '需咨询',
    };
    return transparencyMap[transparency] || transparency;
  };

  const renderFrequencyText = (frequency) => {
    const frequencyMap = {
      frequent: '频繁',
      quite_often: '较多',
      average: '一般',
      rare: '较少',
      never: '几乎没有',
    };
    return frequencyMap[frequency] || frequency;
  };

  const renderAttractivenessText = (attractiveness) => {
    const attractivenessMap = {
      very_attractive: '非常有吸引力',
      attractive: '有吸引力',
      average: '一般',
      unattractive: '吸引力较弱',
      poor: '几乎无吸引力',
    };
    return attractivenessMap[attractiveness] || attractiveness;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <Spin />
        <p>加载价格与会员体系信息...</p>
      </div>
    );
  }

  if (isEditing) {
    return (
      <div>
        <Title level={4}>
          {pricing ? '编辑价格与会员体系' : '添加价格与会员体系'}
        </Title>
        <PricingForm
          storeId={storeId}
          pricingId={pricing?.id}
          onSuccess={handlePricingSaved}
        />
        <div style={{ marginTop: 16, textAlign: 'center' }}>
          <Button onClick={() => setIsEditing(false)}>取消</Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="价格策略" key="pricing">
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
            <Title level={4}>价格与会员体系</Title>
            {pricing ? (
              <Button
                type="primary"
                icon={<EditOutlined />}
                onClick={handleEditPricing}
              >
                编辑价格信息
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddPricing}
              >
                添加价格信息
              </Button>
            )}
          </div>
          
          {pricing ? (
            <Card>
              <Descriptions title="价格策略" bordered>
                <Descriptions.Item label="整体价格定位">
                  {renderPriceLevelText(pricing.price_level)}
                </Descriptions.Item>
                <Descriptions.Item label="价格透明度">
                  {pricing.price_transparency ? renderTransparencyText(pricing.price_transparency) : '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="是否有会员制度">
                  {pricing.has_membership ? '是' : '否'}
                </Descriptions.Item>
                
                {pricing.has_membership && (
                  <>
                    <Descriptions.Item label="会员卡类型" span={3}>
                      {pricing.membership_types || '无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="办理门槛/费用" span={3}>
                      {pricing.membership_threshold || '无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="会员主要权益" span={3}>
                      {pricing.membership_benefits || '无'}
                    </Descriptions.Item>
                    <Descriptions.Item label="会员体系吸引力评估">
                      {pricing.membership_attractiveness ? renderAttractivenessText(pricing.membership_attractiveness) : '未评估'}
                    </Descriptions.Item>
                  </>
                )}
                
                <Descriptions.Item label="促销活动频率">
                  {pricing.promotion_frequency ? renderFrequencyText(pricing.promotion_frequency) : '未知'}
                </Descriptions.Item>
                <Descriptions.Item label="常规促销活动类型" span={3}>
                  {pricing.promotion_types || '无'}
                </Descriptions.Item>
                
                {pricing.pricing_strategy_notes && (
                  <Descriptions.Item label="价格策略评价" span={3}>
                    {pricing.pricing_strategy_notes}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </Card>
          ) : (
            <div style={{ textAlign: 'center', padding: '30px' }}>
              <Text type="secondary">暂无价格与会员体系信息，请点击"添加价格信息"按钮添加</Text>
            </div>
          )}
        </TabPane>
        
        <TabPane tab="标杆商品比价" key="benchmark">
          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
            <Title level={4}>标杆商品比价</Title>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              disabled={!pricing}
            >
              添加标杆商品
            </Button>
          </div>
          
          {pricing ? (
            <div style={{ textAlign: 'center', padding: '30px' }}>
              <Text>标杆商品比价功能正在开发中，敬请期待...</Text>
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '30px' }}>
              <Text type="secondary">请先添加价格与会员体系基本信息</Text>
            </div>
          )}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PricingManager; 