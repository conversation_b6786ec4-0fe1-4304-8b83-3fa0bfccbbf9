import React, { useState } from 'react';
import { Card, Form, Input, Button, Switch, Select, Divider, Row, Col, Typography, message, Alert, notification, Modal, Descriptions, Space } from 'antd';
import { SyncOutlined, ExportOutlined, ImportOutlined, CloudUploadOutlined } from '@ant-design/icons';
import { pricingAPI, environmentAPI, marketingAPI, staffAPI, ratingAPI } from '../api/api';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const Settings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [migrating, setMigrating] = useState(false);
  const [migrateResults, setMigrateResults] = useState(null);

  const onFinish = (values) => {
    setLoading(true);
    
    // In a real application, this would save to the backend
    console.log('Settings values:', values);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      message.success('设置已保存');
    }, 1000);
  };

  const exportDataToJson = () => {
    message.info('功能开发中...');
  };

  const importDataFromJson = () => {
    message.info('功能开发中...');
  };

  const handleMigrateLocalStorageData = async () => {
    Modal.confirm({
      title: '确认迁移数据',
      content: '此操作将尝试将浏览器localStorage中的数据迁移到后端数据库。如果数据已存在，将不会重复添加。确认继续？',
      onOk: migrateLocalStorageData,
      okText: '确认迁移',
      cancelText: '取消',
    });
  };

  const migrateLocalStorageData = async () => {
    setMigrating(true);
    setMigrateResults(null);
    
    try {
      const results = {
        pricing: { migrated: 0, error: 0 },
        environment: { migrated: 0, error: 0 },
        marketing: { migrated: 0, error: 0 },
        staff: { migrated: 0, error: 0 },
        rating: { migrated: 0, error: 0 },
      };
      
      // 迁移价格策略数据
      const pricingData = JSON.parse(localStorage.getItem('pricing-strategies') || '[]');
      for (const item of pricingData) {
        try {
          await pricingAPI.create(item);
          results.pricing.migrated++;
        } catch (error) {
          console.error('Error migrating pricing data:', error);
          results.pricing.error++;
        }
      }
      
      // 迁移环境设施数据
      const environmentData = JSON.parse(localStorage.getItem('store-environments') || '[]');
      for (const item of environmentData) {
        try {
          await environmentAPI.create(item);
          results.environment.migrated++;
        } catch (error) {
          console.error('Error migrating environment data:', error);
          results.environment.error++;
        }
      }
      
      // 迁移营销数据
      const marketingData = JSON.parse(localStorage.getItem('marketing-promotions') || '[]');
      for (const item of marketingData) {
        try {
          await marketingAPI.create(item);
          results.marketing.migrated++;
        } catch (error) {
          console.error('Error migrating marketing data:', error);
          results.marketing.error++;
        }
      }
      
      // 迁移人员数据
      const staffData = JSON.parse(localStorage.getItem('staff-operations') || '[]');
      for (const item of staffData) {
        try {
          await staffAPI.create(item);
          results.staff.migrated++;
        } catch (error) {
          console.error('Error migrating staff data:', error);
          results.staff.error++;
        }
      }
      
      // 迁移评分数据
      const ratingData = JSON.parse(localStorage.getItem('comprehensive-ratings') || '[]');
      for (const item of ratingData) {
        try {
          await ratingAPI.create(item);
          results.rating.migrated++;
        } catch (error) {
          console.error('Error migrating rating data:', error);
          results.rating.error++;
        }
      }
      
      setMigrateResults(results);
      
      const totalMigrated = Object.values(results).reduce((sum, item) => sum + item.migrated, 0);
      const totalErrors = Object.values(results).reduce((sum, item) => sum + item.error, 0);
      
      if (totalMigrated > 0) {
        notification.success({
          message: '数据迁移成功',
          description: `成功迁移 ${totalMigrated} 条数据${totalErrors > 0 ? `，${totalErrors} 条数据迁移失败` : ''}。`,
          duration: 5,
        });
      } else if (totalErrors > 0) {
        notification.error({
          message: '数据迁移失败',
          description: `${totalErrors} 条数据迁移失败，请查看详情。`,
          duration: 5,
        });
      } else {
        notification.info({
          message: '没有数据需要迁移',
          description: '未找到localStorage中需要迁移的数据。',
          duration: 5,
        });
      }
    } catch (error) {
      console.error('Error during migration:', error);
      notification.error({
        message: '数据迁移过程中发生错误',
        description: error.message || '未知错误',
        duration: 5,
      });
    } finally {
      setMigrating(false);
    }
  };

  return (
    <div>
      <Card>
        <Title level={2}>系统设置</Title>
        <Divider />
        
        <Title level={4}>数据管理</Title>
        <Paragraph>
          导出、导入和备份系统数据。
        </Paragraph>
        
        <Space direction="vertical" style={{ width: '100%', marginBottom: 20 }}>
          <Button 
            type="primary" 
            icon={<ExportOutlined />} 
            onClick={exportDataToJson}
            block
          >
            导出数据到JSON文件
          </Button>
          
          <Button 
            icon={<ImportOutlined />} 
            onClick={importDataFromJson}
            block
          >
            从JSON文件导入数据
          </Button>
          
          <Divider />
          
          <Alert
            message="localStorage数据迁移"
            description="将浏览器localStorage中存储的数据迁移到后端数据库中。这对于之前使用localStorage临时存储的数据很有用。"
            type="info"
            showIcon
          />
          
          <Button 
            type="primary" 
            icon={<CloudUploadOutlined />} 
            onClick={handleMigrateLocalStorageData}
            loading={migrating}
            block
          >
            迁移localStorage数据到数据库
          </Button>
          
          {migrateResults && (
            <div style={{ marginTop: 16 }}>
              <Descriptions title="迁移结果" bordered>
                <Descriptions.Item label="价格策略数据" span={3}>
                  成功：{migrateResults.pricing.migrated}，
                  失败：{migrateResults.pricing.error}
                </Descriptions.Item>
                <Descriptions.Item label="环境设施数据" span={3}>
                  成功：{migrateResults.environment.migrated}，
                  失败：{migrateResults.environment.error}
                </Descriptions.Item>
                <Descriptions.Item label="营销口碑数据" span={3}>
                  成功：{migrateResults.marketing.migrated}，
                  失败：{migrateResults.marketing.error}
                </Descriptions.Item>
                <Descriptions.Item label="人员运营数据" span={3}>
                  成功：{migrateResults.staff.migrated}，
                  失败：{migrateResults.staff.error}
                </Descriptions.Item>
                <Descriptions.Item label="综合评分数据" span={3}>
                  成功：{migrateResults.rating.migrated}，
                  失败：{migrateResults.rating.error}
                </Descriptions.Item>
              </Descriptions>
            </div>
          )}
        </Space>
        
        <Divider />
        
        <Title level={4}>系统信息</Title>
        <Descriptions bordered>
          <Descriptions.Item label="系统版本" span={3}>1.0.0</Descriptions.Item>
          <Descriptions.Item label="前端框架" span={3}>React + Ant Design</Descriptions.Item>
          <Descriptions.Item label="后端框架" span={3}>Django + Django REST Framework</Descriptions.Item>
          <Descriptions.Item label="数据库" span={3}>SQLite (可切换至MySQL)</Descriptions.Item>
        </Descriptions>
        
      </Card>
    </div>
  );
};

export default Settings; 