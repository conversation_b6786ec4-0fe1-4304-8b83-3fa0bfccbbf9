import React, { useState, useLayoutEffect, useRef, useEffect } from 'react';
import { Card, Spin, message, Tag, Space, Button } from 'antd';
import { EnvironmentOutlined, ReloadOutlined } from '@ant-design/icons';
import {
  loadAMapAPI,
  initMapInstance,
  destroyMapInstance,
  DEFAULT_CENTER,
  DEFAULT_ZOOM
} from '../config/mapConfig';
import { defaultMarkerConfig } from '../config/markerConfig';

// 固定容器ID前缀，但根据组件实例生成唯一ID
const MAP_CONTAINER_ID_PREFIX = 'map-display-container';

const AMapDisplay = ({ location, address, title }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [addressLocation, setAddressLocation] = useState(null);
  const [locationType, setLocationType] = useState('default');
  const mapInstanceRef = useRef(null);
  const containerIdRef = useRef(`${MAP_CONTAINER_ID_PREFIX}-${Math.random().toString(36).substring(2, 9)}`);
  const containerCreatedRef = useRef(false);
  
  // 通过地址获取坐标
  const getLocationByAddress = async (addressText) => {
    if (!addressText) return null;

    try {
      const AMap = await loadAMapAPI();
      
      return new Promise((resolve, reject) => {
        try {
          const geocoder = new AMap.Geocoder();
          geocoder.getLocation(addressText, (status, result) => {
            if (status === 'complete' && result.info === 'OK' && result.geocodes.length > 0) {
              const { location: geoLocation } = result.geocodes[0];
              resolve({
                lng: geoLocation.lng,
                lat: geoLocation.lat,
                formattedAddress: result.geocodes[0].formattedAddress || addressText
              });
            } else {
              reject(new Error('No location found for this address'));
            }
          });
        } catch (error) {
          reject(error);
        }
      });
    } catch (error) {
      console.error('Failed to load AMap API:', error);
      throw error;
    }
  };

  // 创建DOM容器
  const createMapContainer = () => {
    // 检查是否已创建容器
    if (containerCreatedRef.current) return;
    
    // 检查容器是否已存在
    const existingContainer = document.getElementById(containerIdRef.current);
    if (existingContainer) {
      containerCreatedRef.current = true;
      return;
    }
    
    // 创建新的容器
    const mapContainer = document.createElement('div');
    mapContainer.id = containerIdRef.current;
    mapContainer.style.height = '100%';
    mapContainer.style.width = '100%';
    
    // 将容器添加到父元素
    const parentEl = document.getElementById('map-display-wrapper');
    if (parentEl) {
      // 清空父元素
      while (parentEl.firstChild) {
        parentEl.removeChild(parentEl.firstChild);
      }
      parentEl.appendChild(mapContainer);
      containerCreatedRef.current = true;
    }
  };

  // 初始化地图
  const initMap = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // 创建DOM容器
      createMapContainer();
      
      // 确定地图中心点
      let position = DEFAULT_CENTER; // 默认位置
      
      if (location && typeof location.lng === 'number' && typeof location.lat === 'number') {
        // 使用传入的经纬度
        position = [location.lng, location.lat];
        setLocationType('coordinates');
      } else if (address) {
        // 尝试通过地址获取坐标
        try {
          const geoLocation = await getLocationByAddress(address);
          if (geoLocation) {
            position = [geoLocation.lng, geoLocation.lat];
            setAddressLocation(geoLocation);
            setLocationType('geocoded');
          }
        } catch (error) {
          console.error('Error getting location from address:', error);
          setLocationType('default');
        }
      }
      
      // 使用增强的地图初始化方法
      const { map: mapInstance, AMap } = await initMapInstance(containerIdRef.current, {
        zoom: 15,
        center: position,
      });
      
      // 保存地图实例引用
      mapInstanceRef.current = mapInstance;
      
      // 添加工具栏控件
      mapInstance.addControl(new AMap.ToolBar());
      
      // 添加标记
      if ((location && location.lng && location.lat) || addressLocation) {
        const markerPosition = addressLocation ? 
          [addressLocation.lng, addressLocation.lat] : 
          [location.lng, location.lat];
          
        const marker = defaultMarkerConfig.createAMapMarker(AMap, {
          position: markerPosition,
          competitionRelation: 'default',
          title: addressLocation?.formattedAddress || address || '门店位置'
        });
        
        marker.setMap(mapInstance);
        
        // 添加信息窗体
        const infoWindow = new AMap.InfoWindow({
          content: `<div style="padding: 8px;"><b>${addressLocation?.formattedAddress || address || '门店位置'}</b></div>`,
          offset: new AMap.Pixel(0, -30)
        });
        
        marker.on('mouseover', () => {
          infoWindow.open(mapInstance, marker.getPosition());
        });
        
        marker.on('mouseout', () => {
          infoWindow.close();
        });
      }
    } catch (error) {
      console.error('Failed to initialize map:', error);
      setError('地图初始化失败，请刷新重试');
    } finally {
      setLoading(false);
    }
  };

  // 组件卸载时清理地图
  useEffect(() => {
    return () => {
      // 使用中央管理系统销毁地图实例
      if (containerIdRef.current) {
        destroyMapInstance(containerIdRef.current);
      }
      
      mapInstanceRef.current = null;
      
      // 最后一步：谨慎处理容器DOM
      try {
        const container = document.getElementById(containerIdRef.current);
        if (container && container.parentNode) {
          // 使用父节点的removeChild方法，避免React的DOM管理冲突
          container.parentNode.removeChild(container);
        }
      } catch (e) {
        console.error('Error removing map container:', e);
      }
      
      containerCreatedRef.current = false;
    };
  }, []);

  // 属性变更时重新初始化地图
  useLayoutEffect(() => {
    const timer = setTimeout(() => {
      initMap();
    }, 150);
    
    return () => {
      clearTimeout(timer);
    };
  }, [location, address]);
  
  // 重新加载地图
  const reloadMap = () => {
    if (containerIdRef.current) {
      destroyMapInstance(containerIdRef.current);
    }
    
    mapInstanceRef.current = null;
    containerCreatedRef.current = false;
    
    setTimeout(() => {
      initMap();
    }, 150);
  };

  // 渲染定位方式标签
  const renderLocationTypeTag = () => {
    switch (locationType) {
      case 'coordinates':
        return <Tag color="green">使用门店经纬度坐标定位</Tag>;
      case 'geocoded':
        return <Tag color="blue">通过门店地址自动定位</Tag>;
      case 'default':
        return <Tag color="orange">使用默认位置</Tag>;
      default:
        return null;
    }
  };

  // 渲染地图内容
  const renderMapContent = () => {
    if (error) {
      return (
        <div style={{ height: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column', gap: '16px' }}>
          <div style={{ color: 'red' }}>{error}</div>
          <Button type="primary" icon={<ReloadOutlined />} onClick={reloadMap}>重新加载地图</Button>
        </div>
      );
    }
    
    return (
      <div style={{ height: '300px', width: '100%', position: 'relative' }}>
        <div id="map-display-wrapper" style={{ height: '100%', width: '100%' }}></div>
        {loading && (
          <div style={{ position: 'absolute', top: 0, left: 0, width: '100%', height: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', background: 'rgba(255,255,255,0.7)', zIndex: 10 }}>
            <Spin tip="地图加载中..." />
          </div>
        )}
      </div>
    );
  };

  return (
    <Card 
      title={
        <Space>
          {title || "门店位置"}
          {renderLocationTypeTag()}
        </Space>
      }
      bordered={false}
      style={{ marginBottom: 24 }}
      extra={
        <Space>
          {address && (
            <Space>
              <EnvironmentOutlined />
              <span>{address}</span>
            </Space>
          )}
          <Button 
            size="small" 
            icon={<ReloadOutlined />} 
            onClick={reloadMap} 
            disabled={loading}
          >
            {loading ? '加载中...' : '重新加载地图'}
          </Button>
        </Space>
      }
    >
      {renderMapContent()}
    </Card>
  );
};

export default AMapDisplay; 