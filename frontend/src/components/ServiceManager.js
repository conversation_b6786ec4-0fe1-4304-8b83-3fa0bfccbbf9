import React, { useState, useEffect } from 'react';
import { Table, Button, Space, Modal, message, Tabs, Card, Tag, Typography, Popconfirm } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { serviceAPI } from '../api/api';
import ServiceForm from './ServiceForm';

const { TabPane } = Tabs;
const { Title, Text } = Typography;

const ServiceManager = ({ storeId }) => {
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentServiceId, setCurrentServiceId] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [serviceCategories, setServiceCategories] = useState([]);

  useEffect(() => {
    if (storeId) {
      fetchServices();
    }
  }, [storeId]);

  const fetchServices = async () => {
    setLoading(true);
    try {
      const response = await serviceAPI.getAll({ store: storeId });
      const servicesData = response.data.results || [];
      setServices(servicesData);
      
      // Extract unique service categories
      const categories = [...new Set(servicesData.map(service => service.service_category))];
      setServiceCategories(categories);
    } catch (error) {
      console.error('Error fetching services:', error);
      message.error('获取服务项目失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAddService = () => {
    setCurrentServiceId(null);
    setModalVisible(true);
  };

  const handleEditService = (serviceId) => {
    setCurrentServiceId(serviceId);
    setModalVisible(true);
  };

  const handleDeleteService = async (serviceId) => {
    try {
      await serviceAPI.delete(serviceId);
      message.success('服务项目删除成功');
      fetchServices();
    } catch (error) {
      console.error('Error deleting service:', error);
      message.error('服务项目删除失败');
    }
  };

  const handleModalClose = () => {
    setModalVisible(false);
    setCurrentServiceId(null);
  };

  const handleServiceSaved = () => {
    fetchServices();
    setModalVisible(false);
  };

  const columns = [
    {
      title: '服务类别',
      dataIndex: 'service_category',
      key: 'service_category',
    },
    {
      title: '服务名称',
      dataIndex: 'service_name',
      key: 'service_name',
    },
    {
      title: '价格/价格范围',
      dataIndex: 'price_range',
      key: 'price_range',
      render: (text) => text || '-',
    },
    {
      title: '会员价/套餐价',
      dataIndex: 'member_price',
      key: 'member_price',
      render: (text) => text || '-',
    },
    {
      title: '使用产品品牌',
      dataIndex: 'product_brand',
      key: 'product_brand',
      render: (text) => text || '-',
    },
    {
      title: '独立猫咪区',
      dataIndex: 'has_cat_area',
      key: 'has_cat_area',
      render: (hasCatArea) => (hasCatArea ? <Tag color="green">有</Tag> : <Tag color="default">无</Tag>),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            icon={<EditOutlined />}
            size="small"
            onClick={() => handleEditService(record.id)}
          />
          <Popconfirm
            title="确定要删除此服务项目吗？"
            onConfirm={() => handleDeleteService(record.id)}
            okText="确定"
            cancelText="取消"
            icon={<ExclamationCircleOutlined style={{ color: 'red' }} />}
          >
            <Button
              type="primary"
              danger
              icon={<DeleteOutlined />}
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  const filteredServices = activeTab === 'all' 
    ? services 
    : services.filter(service => service.service_category === activeTab);

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
        <Title level={4}>服务项目管理</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddService}
        >
          添加服务项目
        </Button>
      </div>
      
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="全部服务" key="all" />
        {serviceCategories.map(category => (
          <TabPane tab={category} key={category} />
        ))}
      </Tabs>
      
      <Table
        columns={columns}
        dataSource={filteredServices}
        rowKey="id"
        loading={loading}
        expandable={{
          expandedRowRender: (record) => (
            <div style={{ padding: '0 20px' }}>
              {record.service_description && (
                <div style={{ marginBottom: 8 }}>
                  <Text strong>服务内容描述：</Text> {record.service_description}
                </div>
              )}
              {record.appointment_methods && (
                <div style={{ marginBottom: 8 }}>
                  <Text strong>预约方式：</Text> {
                    (() => {
                      let methods = [];
                      try {
                        methods = JSON.parse(record.appointment_methods);
                      } catch (e) {
                        methods = record.appointment_methods.split(',');
                      }
                      
                      const methodMap = {
                        phone: '电话预约',
                        wechat: '微信预约',
                        miniapp: '小程序预约',
                        platform: '线上平台预约',
                        walk_in: '现场预约',
                      };
                      
                      return methods.map(method => methodMap[method] || method).join('，');
                    })()
                  }
                </div>
              )}
              {record.waiting_area_facilities && (
                <div style={{ marginBottom: 8 }}>
                  <Text strong>等待区设施：</Text> {record.waiting_area_facilities}
                </div>
              )}
              {record.notes && (
                <div>
                  <Text strong>备注：</Text> {record.notes}
                </div>
              )}
            </div>
          ),
        }}
      />
      
      <Modal
        title={currentServiceId ? "编辑服务项目" : "添加服务项目"}
        open={modalVisible}
        onCancel={handleModalClose}
        footer={null}
        width={800}
      >
        <ServiceForm
          storeId={storeId}
          serviceId={currentServiceId}
          onSuccess={handleServiceSaved}
        />
      </Modal>
    </div>
  );
};

export default ServiceManager; 