import React, { useState, useEffect, useRef } from 'react';
import { Card, Spin, message, Button, Space, Tooltip, Radio } from 'antd';
import { ReloadOutlined, EyeOutlined, InfoCircleOutlined, ShopOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { storeAPI } from '../api/api';
import {
  loadAMapAPI,
  initMapInstance,
  destroyMapInstance,
  DEFAULT_CENTER,
  DEFAULT_ZOOM
} from '../config/mapConfig';
import { defaultMarkerConfig, getMarkerLabel } from '../config/markerConfig';

const StoreMapDisplay = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [mapLoading, setMapLoading] = useState(true);
  const [stores, setStores] = useState([]);
  const [error, setError] = useState(null);
  
  // 地图相关状态
  const mapContainerRef = useRef(null);
  const mapInstanceRef = useRef(null);
  const aMapRef = useRef(null);
  const markersRef = useRef([]);
  const mapMountedRef = useRef(false);
  const mapContainerIdRef = useRef(`store-map-display-container-${Math.random().toString(36).substring(2, 9)}`);

  // 地图显示模式（全部/直接竞争对手/间接竞争对手）
  const [displayMode, setDisplayMode] = useState('all');
  
  // 生成唯一的地图容器ID
  const generateMapContainerId = (prefix) => {
    const timestamp = new Date().getTime();
    const random = Math.floor(Math.random() * 10000);
    return `${prefix}-${timestamp}-${random}`;
  };
  
  // 加载门店数据
  const loadStores = async () => {
    try {
      setLoading(true);
      const response = await storeAPI.getAll({
        page_size: 1000 // 请求较大数量的数据，避免分页限制
      });
      
      console.log('加载门店数据:', response.data.results.length, '条记录');
      
      // 过滤出有经纬度的门店数据
      const validStores = response.data.results.filter(
        store => store.longitude && store.latitude
      );
      
      console.log('有效门店数据(含经纬度):', validStores.length, '条记录');
      
      // 对于没有经纬度的门店，尝试使用地址获取坐标
      const storesWithoutCoords = response.data.results.filter(
        store => (!store.longitude || !store.latitude) && store.address
      );
      
      console.log('无经纬度但有地址的门店:', storesWithoutCoords.length, '条记录');
      
      setStores(validStores);
      
      if (validStores.length === 0) {
        message.info('没有找到含有定位信息的门店');
      }
    } catch (error) {
      console.error('Failed to load stores:', error);
      setError('加载门店数据失败');
      message.error('加载门店数据失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 初始化地图
  const initMap = async () => {
    setMapLoading(true);
    setError(null);
    
    try {
      // 确保容器元素已经渲染
      if (!mapContainerRef.current) {
        console.error('Map container ref is not available');
        setError('地图容器初始化失败');
        setMapLoading(false);
        return;
      }
      
      // 生成唯一ID并设置到容器
      const uniqueMapId = generateMapContainerId('store-map');
      mapContainerRef.current.id = uniqueMapId;
      mapContainerIdRef.current = uniqueMapId;
      
      // 初始化地图，增加重试次数和间隔
      const { map, AMap } = await initMapInstance(uniqueMapId, {
        zoom: DEFAULT_ZOOM,
        center: DEFAULT_CENTER
      }, 5, 500);
      
      mapInstanceRef.current = map;
      aMapRef.current = AMap;
      mapMountedRef.current = true;
      
      // 添加工具栏控件
      map.addControl(new AMap.ToolBar());
      
      // 添加比例尺控件
      map.addControl(new AMap.Scale());
      
      setMapLoading(false);
    } catch (error) {
      console.error('Failed to initialize map:', error);
      setError('地图初始化失败');
      setMapLoading(false);
    }
  };
  
  // 在地图上添加门店标记
  const addStoreMarkers = (filteredStores) => {
    // 组件已卸载则不执行
    if (!mapMountedRef.current) return;
    
    console.log('开始添加门店标记，数据量:', filteredStores.length);
    
    // 清除之前的门店标记
    if (markersRef.current.length > 0) {
      markersRef.current.forEach(marker => {
        if (marker && marker.getMap && marker.getMap()) {
          marker.setMap(null);
        }
      });
      markersRef.current = [];
    }
    
    if (!aMapRef.current || !mapInstanceRef.current || filteredStores.length === 0) {
      console.log('无法添加标记: AMap不可用或无数据');
      return;
    }
    
    // 使用统一的图标配置管理
    
    // 批量创建标记以提高性能
    const markers = [];
    const infoWindows = [];
    
    console.log('正在创建标记，总数:', filteredStores.length);
    
    filteredStores.forEach((store, index) => {
      if (!store.longitude || !store.latitude) {
        console.log(`跳过无效坐标的门店 ${index}:`, store.name, store.id);
        return;
      }
      
      const lng = parseFloat(store.longitude);
      const lat = parseFloat(store.latitude);
      
      if (isNaN(lng) || isNaN(lat) || !isFinite(lng) || !isFinite(lat)) {
        console.log(`跳过无效坐标值的门店 ${index}:`, store.name, store.id, lng, lat);
        return;
      }
      
      if (lng < -180 || lng > 180 || lat < -90 || lat > 90) {
        console.log(`跳过坐标范围异常的门店 ${index}:`, store.name, store.id, lng, lat);
        return;
      }
      
      try {
        // 使用统一的标记配置创建标记
        const marker = defaultMarkerConfig.createAMapMarker(aMapRef.current, {
          position: [lng, lat],
          competitionRelation: store.competition_relation || 'default',
          title: store.name,
          zIndex: 100
        });
        
        markers.push(marker);
        
        // 创建信息窗体但暂不添加到地图
        const createInfoWindowContent = (store) => {
          // 获取竞争关系对应的颜色
          let relationColor = '#1890ff'; // 默认蓝色
          if (store.competition_relation === 'direct_competitor') {
            relationColor = '#f5222d'; // 红色
          } else if (store.competition_relation === 'indirect_hospital') {
            relationColor = '#faad14'; // 黄色
          } else if (store.competition_relation === 'indirect_retail') {
            relationColor = '#52c41a'; // 绿色
          }
          
          return `
            <div style="padding: 12px; max-width: 300px;">
              <h4 style="margin: 0 0 8px 0; border-bottom: 1px solid #eee; padding-bottom: 8px;">${store.name}</h4>
              
              <div style="margin-bottom: 3px;"><b>品牌:</b> ${store.brand_name || '未知'}</div>
              <div style="margin-bottom: 3px;"><b>门店类型:</b> ${getStoreTypeText(store.store_type)}</div>
              <div style="margin-bottom: 3px;"><b>竞争关系:</b> <span style="color: ${relationColor};">${getCompetitionRelationText(store.competition_relation)}</span></div>
              <div style="margin-bottom: 3px;"><b>地址:</b> ${store.address || '未知'}</div>
              
              <div style="margin-top: 12px; text-align: center;">
                <a href="javascript:void(0)" id="view-store-${store.id}" style="
                  background-color: #1890ff; 
                  color: white; 
                  padding: 6px 15px; 
                  text-decoration: none; 
                  border-radius: 4px;
                  display: inline-block;
                ">查看详情</a>
              </div>
            </div>
          `;
        };
        
        const infoWindow = new aMapRef.current.InfoWindow({
          content: createInfoWindowContent(store),
          offset: new aMapRef.current.Pixel(0, -30),
          closeWhenClickMap: true
        });
        
        infoWindows.push(infoWindow);
        
        // 添加点击事件监听器
        marker.on('click', () => {
          if (!mapMountedRef.current) return;
          
          infoWindow.open(mapInstanceRef.current, marker.getPosition());
          
          // 设置延时以确保DOM已经渲染
          setTimeout(() => {
            if (!mapMountedRef.current) return;
            
            const detailLink = document.getElementById(`view-store-${store.id}`);
            if (detailLink) {
              detailLink.addEventListener('click', (e) => {
                e.preventDefault();
                navigate(`/view-store/${store.id}`);
              });
            }
          }, 100);
        });
      } catch (error) {
        console.error(`创建标记失败 ${index}:`, store.name, error);
      }
    });
    
    console.log('成功创建标记数量:', markers.length);
    
    // 批量将标记添加到地图
    try {
      mapInstanceRef.current.add(markers);
      console.log('标记已添加到地图');
      
      // 自动调整视图以显示所有标记
      if (markers.length > 0) {
        mapInstanceRef.current.setFitView(null, false, [50, 50, 50, 50]);
      }
      
      // 保存标记引用以便后续清理
      markersRef.current = markers;
    } catch (error) {
      console.error('批量添加标记到地图失败:', error);
    }
  };

  // 获取门店类型文本
  const getStoreTypeText = (type) => {
    const typeMap = {
      'direct_chain': '直营连锁',
      'franchise_chain': '加盟连锁',
      'single_store': '单体店',
      'pet_hospital': '宠物医院附带',
      'retail_only': '纯零售店',
    };
    return typeMap[type] || type;
  };
  
  // 获取竞争关系文本（使用统一配置）
  const getCompetitionRelationText = (relation) => {
    return getMarkerLabel(relation);
  };
  
  // 更新地图显示
  const updateMapDisplay = () => {
    if (!mapMountedRef.current || !mapInstanceRef.current || !aMapRef.current) return;
    
    console.log('更新地图显示，当前门店数据:', stores.length, '条记录');
    
    if (stores.length === 0) {
      message.info('没有可显示的门店数据');
      return;
    }
    
    let filteredStores = [...stores];
    
    // 根据显示模式过滤门店
    if (displayMode === 'direct') {
      filteredStores = stores.filter(store => store.competition_relation === 'direct_competitor');
      console.log('过滤后的直接竞争对手:', filteredStores.length, '条记录');
    } else if (displayMode === 'indirect') {
      filteredStores = stores.filter(store => 
        store.competition_relation === 'indirect_hospital' || 
        store.competition_relation === 'indirect_retail'
      );
      console.log('过滤后的间接竞争对手:', filteredStores.length, '条记录');
    }
    
    // 添加标记到地图
    addStoreMarkers(filteredStores);
  };
  
  // 处理显示模式变化
  const handleDisplayModeChange = (e) => {
    setDisplayMode(e.target.value);
  };
  
  // 重新加载数据
  const handleReload = () => {
    message.info('重新加载门店数据...');
    loadStores();
  };
  
  // 清理地图资源
  const cleanupMap = () => {
    // 标记组件已卸载
    mapMountedRef.current = false;
    
    // 清除所有标记
    if (markersRef.current && markersRef.current.length > 0) {
      markersRef.current.forEach(marker => {
        if (marker && marker.getMap && marker.getMap()) {
          try {
            marker.setMap(null);
          } catch (e) {
            console.warn('Error removing marker:', e);
          }
        }
      });
      markersRef.current = [];
    }
    
    // 保存容器ID的副本，避免在cleanup中访问可能已更改的ref
    const currentMapId = mapContainerIdRef.current;
    
    // 延迟销毁地图，确保React DOM操作完成
    setTimeout(() => {
      try {
        destroyMapInstance(currentMapId);
      } catch (e) {
        console.warn('Error destroying map instance:', e);
      }
      
      mapInstanceRef.current = null;
      aMapRef.current = null;
    }, 0);
  };
  
  // 初始化
  useEffect(() => {
    // 使用短暂延时确保DOM已完全渲染
    const timer = setTimeout(() => {
      initMap();
      loadStores();
    }, 200);
    
    // 组件卸载时清理
    return () => {
      clearTimeout(timer);
      cleanupMap();
    };
  }, []);
  
  // 当门店数据或显示模式变化时更新标记
  useEffect(() => {
    if (!loading && !mapLoading && mapMountedRef.current) {
      updateMapDisplay();
    }
  }, [stores, displayMode, loading, mapLoading]);
  
  return (
    <Card 
      title={
        <Space>
          <ShopOutlined /> 门店地图展示
          <Tooltip title="点击标记可查看门店简略信息，并跳转到详情页面">
            <InfoCircleOutlined style={{ marginLeft: 8, cursor: 'help' }} />
          </Tooltip>
        </Space>
      }
      extra={
        <Button type="primary" icon={<ReloadOutlined />} onClick={handleReload} loading={loading}>
          刷新数据
        </Button>
      }
    >
      <div style={{ marginBottom: 16 }}>
        <Radio.Group value={displayMode} onChange={handleDisplayModeChange} buttonStyle="solid">
          <Radio.Button value="all">全部门店</Radio.Button>
          <Radio.Button value="direct">直接竞争对手</Radio.Button>
          <Radio.Button value="indirect">间接竞争对手</Radio.Button>
        </Radio.Group>
        
        <div style={{ float: 'right' }}>
          <Space>
            <span style={{ display: 'inline-block', width: 15, height: 15, backgroundColor: '#f5222d', verticalAlign: 'middle' }}></span>
            <span>直接竞争对手</span>
            
            <span style={{ display: 'inline-block', width: 15, height: 15, backgroundColor: '#faad14', verticalAlign: 'middle', marginLeft: 8 }}></span>
            <span>间接竞争对手-医院</span>
            
            <span style={{ display: 'inline-block', width: 15, height: 15, backgroundColor: '#52c41a', verticalAlign: 'middle', marginLeft: 8 }}></span>
            <span>间接竞争对手-零售</span>
          </Space>
        </div>
      </div>
      
      <div 
        style={{ height: 600, width: '100%', position: 'relative' }}
        ref={mapContainerRef}
      >
        {(mapLoading || loading) && (
          <div style={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            width: '100%', 
            height: '100%', 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            background: 'rgba(255,255,255,0.7)', 
            zIndex: 999 
          }}>
            <Spin size="large" tip={loading ? "加载门店数据..." : "初始化地图..."} />
          </div>
        )}
        
        {error && (
          <div style={{ 
            position: 'absolute', 
            top: 0, 
            left: 0, 
            width: '100%', 
            height: '100%', 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center', 
            background: 'rgba(255,255,255,0.9)', 
            zIndex: 1000,
            flexDirection: 'column' 
          }}>
            <div style={{ color: 'red', marginBottom: 16 }}>{error}</div>
            <Button type="primary" onClick={() => {
              setError(null);
              setTimeout(initMap, 200);
            }}>重试</Button>
          </div>
        )}
      </div>
      
      <div style={{ marginTop: 16, textAlign: 'center' }}>
        <b>提示：</b> 点击地图标记点可查看门店简略信息，点击"查看详情"跳转到门店详情页面
      </div>
    </Card>
  );
};

export default StoreMapDisplay; 