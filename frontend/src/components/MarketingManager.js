import React, { useState, useEffect } from 'react';
import { Card, Form, Input, Button, Select, Switch, InputNumber, Space, Typography, Row, Col, message, Divider, Tabs } from 'antd';
import { SaveOutlined, LinkOutlined, PlusOutlined } from '@ant-design/icons';
import { marketingAPI } from '../api/api';
import ActivityManager from './ActivityManager';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

const MarketingManager = ({ storeId, viewMode = false }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('1');
  
  useEffect(() => {
    if (storeId) {
      fetchMarketingData();
    }
  }, [storeId]);
  
  const fetchMarketingData = async () => {
    setLoading(true);
    try {
      const response = await marketingAPI.getAll({ store: storeId });
      const marketingData = response.data.results;
      
      if (marketingData && marketingData.length > 0) {
        form.setFieldsValue(marketingData[0]);
      }
    } catch (error) {
      console.error('Error fetching marketing data:', error);
      message.error('获取营销数据失败');
    } finally {
      setLoading(false);
    }
  };
  
  const onFinish = async (values) => {
    setSaving(true);
    try {
      const marketingData = {
        ...values,
        store: storeId
      };
      
      // Check if record exists, update or create accordingly
      const existingData = await marketingAPI.getAll({ store: storeId });
      if (existingData.data.results && existingData.data.results.length > 0) {
        const id = existingData.data.results[0].id;
        await marketingAPI.update(id, marketingData);
        message.success('营销信息更新成功');
      } else {
        await marketingAPI.create(marketingData);
        message.success('营销信息添加成功');
      }
    } catch (error) {
      console.error('Error saving marketing data:', error);
      message.error('营销信息保存失败');
    } finally {
      setSaving(false);
    }
  };
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '20px' }}>
        <div>加载营销信息...</div>
      </div>
    );
  }
  
  return (
    <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="营销与口碑信息" key="1">
          <Form
            form={form}
            layout="vertical"
            onFinish={onFinish}
            disabled={viewMode}
          >
            <Title level={5}>线上渠道</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="website_url"
                  label="官方网站URL"
                >
                  <Input prefix={<LinkOutlined />} placeholder="https://example.com" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="wechat_account"
                  label="微信公众号名称/ID"
                >
                  <Input placeholder="微信公众号名称或ID" />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="wechat_miniapp"
                  label="微信小程序名称"
                >
                  <Input placeholder="微信小程序名称" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="xiaohongshu_account"
                  label="小红书账号"
                >
                  <Input placeholder="小红书账号" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="douyin_account"
                  label="抖音账号"
                >
                  <Input placeholder="抖音账号" />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="dianping_url"
                  label="大众点评店铺链接"
                >
                  <Input prefix={<LinkOutlined />} placeholder="https://dianping.com/shop/..." />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="meituan_url"
                  label="美团店铺链接"
                >
                  <Input prefix={<LinkOutlined />} placeholder="https://meituan.com/shop/..." />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="online_activity"
                  label="线上活跃度评估"
                >
                  <Select placeholder="请选择线上活跃度">
                    <Option value="very_active">非常活跃</Option>
                    <Option value="active">活跃</Option>
                    <Option value="average">一般</Option>
                    <Option value="inactive">不活跃</Option>
                    <Option value="none">基本无</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="dianping_score"
                  label="大众点评评分"
                >
                  <InputNumber min={0} max={5} step={0.1} style={{ width: '100%' }} placeholder="评分，如4.8" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="dianping_reviews"
                  label="大众点评评论数"
                >
                  <InputNumber min={0} style={{ width: '100%' }} placeholder="评论数量" />
                </Form.Item>
              </Col>
            </Row>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="meituan_score"
                  label="美团评分"
                >
                  <InputNumber min={0} max={5} step={0.1} style={{ width: '100%' }} placeholder="评分，如4.8" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="meituan_reviews"
                  label="美团评论数"
                >
                  <InputNumber min={0} style={{ width: '100%' }} placeholder="评论数量" />
                </Form.Item>
              </Col>
            </Row>
            
            <Divider orientation="left">评价情况</Divider>
            
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="positive_keywords"
                  label="主要好评关键词"
                >
                  <TextArea
                    placeholder="例如：环境好、服务专业、美容效果佳等，用逗号分隔"
                    autoSize={{ minRows: 2, maxRows: 6 }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="negative_keywords"
                  label="主要差评关键词"
                >
                  <TextArea
                    placeholder="例如：价格贵、等待时间长等，用逗号分隔"
                    autoSize={{ minRows: 2, maxRows: 6 }}
                  />
                </Form.Item>
              </Col>
            </Row>
            
            <Form.Item
              name="offline_marketing"
              label="线下营销活动记录"
            >
              <TextArea
                placeholder="记录线下营销活动的方式、频率、效果等"
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </Form.Item>
            
            <Form.Item
              name="brand_perception"
              label="品牌形象感知"
            >
              <TextArea
                placeholder="描述品牌在顾客心目中的形象定位、认知等"
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </Form.Item>
            
            {!viewMode && (
              <Form.Item>
                <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={saving}>
                  保存
                </Button>
              </Form.Item>
            )}
          </Form>
          
          <Divider />
          
          <div style={{ marginTop: 24, textAlign: 'center' }}>
            <Space direction="vertical" align="center">
              <Text type="secondary">切换到"竞品活动追踪"选项卡，可以详细记录该门店的促销活动、营销事件等</Text>
              <Button 
                type="primary" 
                icon={<PlusOutlined />}
                onClick={() => setActiveTab('2')}
              >
                管理竞品活动
              </Button>
            </Space>
          </div>
        </TabPane>
        
        <TabPane tab="竞品活动追踪" key="2">
          <ActivityManager storeId={storeId} />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default MarketingManager; 