import React, { useState, useEffect } from 'react';
import { 
  Form, 
  Input, 
  Button, 
  Select, 
  DatePicker, 
  Space, 
  Card, 
  Divider, 
  message, 
  Row, 
  Col,
  Upload
} from 'antd';
import { activityAPI, activityImageAPI } from '../api/api';
import { PlusOutlined } from '@ant-design/icons';
import moment from 'moment';

const { TextArea } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

const ActivityForm = ({ storeId, activityId, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [isEdit, setIsEdit] = useState(false);
  const [photos, setPhotos] = useState([]);
  
  useEffect(() => {
    if (activityId) {
      setIsEdit(true);
      fetchActivityData();
    } else {
      form.setFieldsValue({ 
        store: storeId,
        status: 'upcoming',
        activity_type: 'promotion',
        impact_level: 'medium'
      });
    }
  }, [storeId, activityId, form]);

  const fetchActivityData = async () => {
    setLoading(true);
    try {
      const response = await activityAPI.getById(activityId);
      const activityData = response.data;
      
      // 处理日期数据格式
      if (activityData.start_date) {
        activityData.start_date = moment(activityData.start_date);
      }
      if (activityData.end_date) {
        activityData.end_date = moment(activityData.end_date);
      }
      
      // 如果有开始和结束日期，设置日期范围
      if (activityData.start_date && activityData.end_date) {
        form.setFieldsValue({
          ...activityData,
          date_range: [activityData.start_date, activityData.end_date]
        });
      } else {
        form.setFieldsValue(activityData);
      }
      
      // 如果有主图片，加载图片数据
      if (activityData.main_image) {
        setPhotos([{
          uid: '-1',
          name: 'main_image.jpg',
          status: 'done',
          url: activityData.main_image
        }]);
      }
      
      // 获取该活动的所有图片
      if (activityId) {
        try {
          const imagesResponse = await activityImageAPI.getAll({ activity: activityId });
          if (imagesResponse.data && imagesResponse.data.results && imagesResponse.data.results.length > 0) {
            const additionalPhotos = imagesResponse.data.results.map((img, index) => ({
              uid: img.id.toString(),
              name: `activity_image_${index+1}.jpg`,
              status: 'done',
              url: img.image
            }));
            setPhotos(prevPhotos => [...prevPhotos, ...additionalPhotos]);
          }
        } catch (error) {
          console.error('Error fetching activity images:', error);
        }
      }
    } catch (error) {
      console.error('Error fetching activity data:', error);
      message.error('获取活动信息失败');
    } finally {
      setLoading(false);
    }
  };

  const handlePhotosChange = (info) => {
    // Make sure fileList exists before processing it
    if (info && info.fileList) {
      setPhotos(info.fileList);
    } else {
      setPhotos([]);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    
    // 处理日期范围
    if (values.date_range && values.date_range.length === 2) {
      values.start_date = values.date_range[0].format('YYYY-MM-DD');
      values.end_date = values.date_range[1].format('YYYY-MM-DD');
    }
    delete values.date_range;
    
    try {
      let actId = activityId;
      
      if (isEdit) {
        await activityAPI.update(activityId, values);
        message.success('活动信息更新成功');
      } else {
        const response = await activityAPI.create(values);
        actId = response.data.id;
        message.success('活动信息添加成功');
        form.resetFields();
        form.setFieldsValue({ 
          store: storeId,
          status: 'upcoming',
          activity_type: 'promotion',
          impact_level: 'medium'
        });
      }
      
      // 处理图片上传
      if (photos.length > 0) {
        const mainPhoto = photos[0]; // 使用第一张照片作为主图片
        if (mainPhoto.originFileObj) {
          await activityAPI.uploadMainImage(actId, mainPhoto.originFileObj);
        }
        
        // 上传其他照片（如果有的话）
        if (photos.length > 1) {
          // 从索引1开始，因为索引0的照片已经作为主图片上传
          for (let i = 1; i < photos.length; i++) {
            const photo = photos[i];
            if (photo.originFileObj) {
              await activityImageAPI.create({
                activity: actId,
                image: photo.originFileObj,
                caption: `活动照片 ${i+1}`,
                order: i
              });
            }
          }
        }
        
        // 如果是编辑模式，清除照片状态以便下次上传
        if (isEdit) {
          setPhotos([]);
        }
      }
      
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error saving activity:', error);
      message.error(isEdit ? '活动信息更新失败' : '活动信息添加失败');
    } finally {
      setLoading(false);
    }
  };

  const uploadButton = (
    <div>
      <PlusOutlined />
      <div style={{ marginTop: 8 }}>上传图片</div>
    </div>
  );

  return (
    <Card>
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
      >
        <Form.Item name="store" hidden>
          <Input />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={16}>
            <Form.Item
              name="title"
              label="活动标题"
              rules={[{ required: true, message: '请输入活动标题' }]}
            >
              <Input placeholder="请输入活动标题" />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="activity_type"
              label="活动类型"
              rules={[{ required: true, message: '请选择活动类型' }]}
            >
              <Select placeholder="请选择活动类型">
                <Option value="promotion">促销活动</Option>
                <Option value="event">营销活动/事件</Option>
                <Option value="new_product">新品发布</Option>
                <Option value="price_change">价格变动</Option>
                <Option value="service_change">服务调整</Option>
                <Option value="store_change">门店变动</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="description"
          label="活动描述"
          rules={[{ required: true, message: '请输入活动描述' }]}
        >
          <TextArea
            placeholder="请详细描述活动内容、规则等"
            autoSize={{ minRows: 3, maxRows: 5 }}
          />
        </Form.Item>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="date_range"
              label="活动日期"
              rules={[{ required: true, message: '请选择活动日期范围' }]}
            >
              <RangePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="status"
              label="活动状态"
              rules={[{ required: true, message: '请选择活动状态' }]}
            >
              <Select placeholder="请选择活动状态">
                <Option value="upcoming">即将开始</Option>
                <Option value="active">进行中</Option>
                <Option value="ended">已结束</Option>
                <Option value="recurring">周期性</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="impact_level"
              label="影响级别"
              rules={[{ required: true, message: '请选择影响级别' }]}
            >
              <Select placeholder="请选择影响级别">
                <Option value="high">高影响</Option>
                <Option value="medium">中等影响</Option>
                <Option value="low">低影响</Option>
                <Option value="unknown">影响未知</Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>
        
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="discount_amount"
              label="折扣力度"
            >
              <Input placeholder="例如：满300减50、8折优惠等" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="target_customers"
              label="目标客户群体"
            >
              <Input placeholder="例如：新客户、会员、单次消费超过200元的客户等" />
            </Form.Item>
          </Col>
        </Row>
        
        <Form.Item
          name="participation_threshold"
          label="参与门槛"
        >
          <TextArea
            placeholder="例如：需要成为会员、需要购买特定产品等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="marketing_channels"
          label="营销渠道"
        >
          <TextArea
            placeholder="例如：微信公众号、线下海报、店内宣传等"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="response_strategy"
          label="应对策略"
        >
          <TextArea
            placeholder="针对该竞品活动，我方可能的应对策略"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          name="results"
          label="活动效果"
        >
          <TextArea
            placeholder="活动效果观察与评估（活动结束后填写）"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Form.Item
          label="活动照片/海报"
        >
          <Upload
            listType="picture-card"
            fileList={photos}
            onChange={handlePhotosChange}
            beforeUpload={() => false} // 阻止自动上传
          >
            {photos.length >= 8 ? null : uploadButton}
          </Upload>
          <div style={{ marginTop: 8 }}>
            <small>第一张照片将作为活动主图片显示，最多可上传8张照片</small>
          </div>
        </Form.Item>
        
        <Form.Item
          name="notes"
          label="备注"
        >
          <TextArea
            placeholder="其他需要备注的信息"
            autoSize={{ minRows: 2, maxRows: 4 }}
          />
        </Form.Item>
        
        <Divider />
        
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              {isEdit ? '更新活动信息' : '添加活动信息'}
            </Button>
            <Button onClick={() => form.resetFields()}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default ActivityForm; 