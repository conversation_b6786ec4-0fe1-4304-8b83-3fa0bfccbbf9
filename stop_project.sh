#!/bin/bash

# 宠物竞争对手分析系统停止脚本
echo "🛑 停止宠物竞争对手分析系统..."

# 停止后端服务
if [ -f "logs/backend.pid" ]; then
    BACKEND_PID=$(cat logs/backend.pid)
    if ps -p $BACKEND_PID > /dev/null; then
        echo "🔴 停止后端服务 (PID: $BACKEND_PID)..."
        kill $BACKEND_PID
        rm logs/backend.pid
    else
        echo "⚠️ 后端服务已停止"
        rm -f logs/backend.pid
    fi
else
    echo "⚠️ 未找到后端服务PID文件"
fi

# 停止前端服务
if [ -f "logs/frontend.pid" ]; then
    FRONTEND_PID=$(cat logs/frontend.pid)
    if ps -p $FRONTEND_PID > /dev/null; then
        echo "🔴 停止前端服务 (PID: $FRONTEND_PID)..."
        kill $FRONTEND_PID
        rm logs/frontend.pid
    else
        echo "⚠️ 前端服务已停止"
        rm -f logs/frontend.pid
    fi
else
    echo "⚠️ 未找到前端服务PID文件"
fi

# 强制停止可能残留的进程
echo "🧹 清理残留进程..."
pkill -f "python manage.py runserver" 2>/dev/null || true
pkill -f "npm start" 2>/dev/null || true
pkill -f "react-scripts start" 2>/dev/null || true

echo "✅ 系统已停止"
