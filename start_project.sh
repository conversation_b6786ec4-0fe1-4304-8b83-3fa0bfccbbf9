#!/bin/bash

# 宠物竞争对手分析系统启动脚本
echo "🚀 启动宠物竞争对手分析系统..."

# 检查是否在项目根目录
if [ ! -d "backend" ] || [ ! -d "frontend" ]; then
    echo "❌ 请在项目根目录运行此脚本"
    exit 1
fi

# 启动后端服务
echo "📦 启动后端服务..."
cd backend

# 检查虚拟环境
if [ ! -d "venv" ]; then
    echo "🔧 创建Python虚拟环境..."
    python3 -m venv venv
fi

# 激活虚拟环境并安装依赖
echo "🔧 激活虚拟环境并检查依赖..."
source venv/bin/activate

# 检查是否需要安装依赖
if [ ! -f "venv/installed" ]; then
    echo "📥 安装Python依赖..."
    pip install -r requirements.txt
    touch venv/installed
fi

# 数据库迁移
echo "🗄️ 执行数据库迁移..."
python manage.py makemigrations
python manage.py migrate

# 启动Django服务器（后台运行）
echo "🌐 启动Django服务器 (http://localhost:8000)..."
nohup python manage.py runserver > ../logs/backend.log 2>&1 &
BACKEND_PID=$!
echo $BACKEND_PID > ../logs/backend.pid

cd ..

# 启动前端服务
echo "⚛️ 启动前端服务..."
cd frontend

# 检查是否需要安装依赖
if [ ! -d "node_modules" ]; then
    echo "📥 安装前端依赖..."
    npm install
fi

# 启动前端开发服务器（后台运行）
echo "🌐 启动前端开发服务器 (http://localhost:3000)..."
nohup npm start > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo $FRONTEND_PID > ../logs/frontend.pid

cd ..

# 创建日志目录
mkdir -p logs

echo "✅ 系统启动完成！"
echo ""
echo "📊 服务地址："
echo "   前端: http://localhost:3000"
echo "   后端: http://localhost:8000"
echo "   后端API: http://localhost:8000/api/"
echo ""
echo "📝 日志文件："
echo "   后端日志: logs/backend.log"
echo "   前端日志: logs/frontend.log"
echo ""
echo "🛑 停止服务："
echo "   运行: ./stop_project.sh"
echo ""
echo "🔍 查看日志："
echo "   后端: tail -f logs/backend.log"
echo "   前端: tail -f logs/frontend.log"

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 5

# 检查服务状态
echo "🔍 检查服务状态..."
if curl -s http://localhost:8000 > /dev/null; then
    echo "✅ 后端服务启动成功"
else
    echo "❌ 后端服务启动失败，请检查日志"
fi

if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端服务启动成功"
else
    echo "⏳ 前端服务正在启动中..."
fi

echo ""
echo "🎉 系统已启动，请访问 http://localhost:3000 开始使用！"
