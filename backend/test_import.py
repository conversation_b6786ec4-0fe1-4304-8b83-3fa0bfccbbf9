#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试导入空电话号码的门店
"""

import requests
import json
import sys
import os

# API端点
API_URL = "http://localhost:8000/api/stores/map-import/"

# 测试数据
test_data = {
    "stores": [
        {
            "name": "测试门店 - 空字符串电话",
            "address": "测试地址1",
            "longitude": 120.0,
            "latitude": 30.0,
            "phone": "",  # 空字符串电话号码
            "brand_name": "测试品牌1"
        },
        {
            "name": "测试门店 - 无电话字段",
            "address": "测试地址2",
            "longitude": 120.1,
            "latitude": 30.1,
            # 不提供phone字段
            "brand_name": "测试品牌2"
        },
        {
            "name": "测试门店 - null电话",
            "address": "测试地址3",
            "longitude": 120.2,
            "latitude": 30.2,
            "phone": None,  # null电话号码
            "brand_name": "测试品牌3"
        },
        {
            "name": "测试门店 - 空格电话",
            "address": "测试地址4",
            "longitude": 120.3,
            "latitude": 30.3,
            "phone": "   ",  # 只有空格的电话号码
            "brand_name": "测试品牌4"
        },
        {
            "name": "测试门店 - 列表电话",
            "address": "测试地址5",
            "longitude": 120.4,
            "latitude": 30.4,
            "phone": ["************", "************"],  # 列表形式的电话号码
            "brand_name": "测试品牌5"
        },
        {
            "name": "测试门店 - 数字电话",
            "address": "测试地址6",
            "longitude": 120.5,
            "latitude": 30.5,
            "phone": 12345678,  # 数字形式的电话号码
            "brand_name": "测试品牌6"
        }
    ]
}

def login():
    """登录获取认证令牌"""
    login_url = "http://localhost:8000/api/token/"
    
    # 默认测试用户名和密码
    username = os.environ.get("TEST_USERNAME", "admin")
    password = os.environ.get("TEST_PASSWORD", "admin")
    
    login_data = {
        "username": username,
        "password": password
    }
    
    try:
        response = requests.post(login_url, data=login_data)
        if response.status_code == 200:
            return response.json().get("access")
        else:
            print(f"登录失败: {response.status_code}")
            print(response.text)
            return None
    except Exception as e:
        print(f"登录异常: {str(e)}")
        return None

def test_import():
    """测试导入API"""
    # 获取认证令牌
    token = login()
    if not token:
        print("❌ 无法获取认证令牌，测试终止")
        return False
        
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {token}'
    }
    
    try:
        response = requests.post(API_URL, headers=headers, data=json.dumps(test_data))
        
        # 打印响应状态码和内容
        print(f"Status Code: {response.status_code}")
        print("Response:")
        print(json.dumps(response.json(), indent=2, ensure_ascii=False))
        
        # 检查是否成功
        if response.status_code == 200:
            result = response.json()
            if result.get('code') == 0:
                print("\n✅ 测试成功：API接受了各种类型的电话号码")
                print(f"成功导入: {result.get('success_count')} 条")
                print(f"重复记录: {result.get('duplicate_count')} 条")
                print(f"错误记录: {result.get('error_count')} 条")
                
                if result.get('error_count') > 0:
                    print("\n❗ 警告：有错误记录，但API仍然返回了成功状态码")
                    print("错误详情:")
                    for error in result.get('errors', []):
                        print(f"  - {error.get('error')}")
                
                return True
            else:
                print(f"\n❌ 测试失败：API返回错误码 {result.get('code')}")
                return False
        else:
            print(f"\n❌ 测试失败：HTTP状态码 {response.status_code}")
            return False
            
    except Exception as e:
        print(f"\n❌ 测试异常：{str(e)}")
        return False

if __name__ == "__main__":
    print("开始测试导入各种类型电话号码的门店...")
    success = test_import()
    sys.exit(0 if success else 1) 