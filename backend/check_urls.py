#!/usr/bin/env python
import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pet_competitor_project.settings')
django.setup()

from django.urls import get_resolver
from django.urls.resolvers import URLPattern, URLResolver

def display_urls(urls, prefix=''):
    """递归显示所有URL模式"""
    for url in urls:
        if isinstance(url, URLPattern):
            # 获取URL模式名称
            name = url.name or '(unnamed)'
            # 获取URL模式路径
            path = prefix + str(url.pattern)
            # 打印URL信息
            print(f"{path} - {name}")
        elif isinstance(url, URLResolver):
            # 递归处理URL解析器
            new_prefix = prefix + str(url.pattern)
            display_urls(url.url_patterns, new_prefix)

if __name__ == '__main__':
    # 获取所有URL模式
    resolver = get_resolver()
    print("Available URLs in the project:")
    print("==============================")
    display_urls(resolver.url_patterns)
    print("==============================") 