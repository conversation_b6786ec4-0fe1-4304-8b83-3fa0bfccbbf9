#!/usr/bin/env python
"""
Django服务器管理脚本
提供启动、停止、重启、状态查询等功能
"""
import os
import sys
import time
import signal
import socket
import psutil
import argparse
import subprocess
from pet_competitor_project.config import SERVER_CONFIG

# 服务器PID文件路径
PID_FILE = os.path.join(os.path.dirname(__file__), '.server.pid')

def is_port_available(port):
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) != 0

def find_available_port(start_port=8000, max_attempts=10):
    """查找可用端口"""
    port = start_port
    for _ in range(max_attempts):
        if is_port_available(port):
            return port
        port += 1
    raise RuntimeError(f"无法找到可用端口（尝试范围：{start_port}-{start_port+max_attempts-1}）")

def get_server_pid():
    """获取服务器进程ID"""
    if os.path.exists(PID_FILE):
        with open(PID_FILE, 'r') as f:
            try:
                pid = int(f.read().strip())
                # 检查进程是否存在
                if psutil.pid_exists(pid):
                    # 检查进程是否为Django服务器
                    process = psutil.Process(pid)
                    if 'python' in process.name().lower() and 'manage.py runserver' in ' '.join(process.cmdline()):
                        return pid
            except (ValueError, psutil.NoSuchProcess):
                pass
    return None

def save_server_pid(pid):
    """保存服务器进程ID"""
    with open(PID_FILE, 'w') as f:
        f.write(str(pid))

def remove_pid_file():
    """删除PID文件"""
    if os.path.exists(PID_FILE):
        os.remove(PID_FILE)

def start_server():
    """启动Django服务器"""
    # 检查服务器是否已经在运行
    pid = get_server_pid()
    if pid:
        print(f"服务器已经在运行（PID: {pid}）")
        return
    
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pet_competitor_project.settings')
    
    # 获取服务器配置
    host = SERVER_CONFIG.get('host', '0.0.0.0')
    port = SERVER_CONFIG.get('port', 8000)
    
    # 检查端口是否可用，如果不可用则寻找替代端口
    if not is_port_available(port):
        print(f"警告：端口 {port} 已被占用")
        port = find_available_port(port)
        print(f"使用替代端口: {port}")
    
    # 构建启动命令
    cmd = [
        sys.executable, 'manage.py', 'runserver',
        f"{host}:{port}"
    ]
    
    # 如果配置了调试模式，添加相应参数
    if SERVER_CONFIG.get('debug', False):
        cmd.append('--noreload')
    
    print(f"启动服务器: {' '.join(cmd)}")
    
    # 启动服务器（后台运行）
    process = subprocess.Popen(
        cmd,
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE,
        start_new_session=True
    )
    
    # 保存进程ID
    save_server_pid(process.pid)
    
    # 等待一段时间，确保服务器正常启动
    time.sleep(2)
    
    # 检查服务器是否成功启动
    if process.poll() is None:
        print(f"服务器已成功启动（PID: {process.pid}）")
    else:
        stdout, stderr = process.communicate()
        print("服务器启动失败:")
        print(stderr.decode())
        remove_pid_file()

def stop_server():
    """停止Django服务器"""
    pid = get_server_pid()
    if pid:
        try:
            os.kill(pid, signal.SIGTERM)
            print(f"已发送终止信号到服务器进程（PID: {pid}）")
            
            # 等待进程终止
            max_wait = 5  # 最多等待5秒
            for _ in range(max_wait):
                if not psutil.pid_exists(pid):
                    break
                time.sleep(1)
            
            # 如果进程仍然存在，强制终止
            if psutil.pid_exists(pid):
                os.kill(pid, signal.SIGKILL)
                print(f"服务器进程未响应，已强制终止（PID: {pid}）")
            
            remove_pid_file()
            print("服务器已停止")
        except OSError as e:
            print(f"停止服务器失败: {e}")
    else:
        print("服务器未运行")

def restart_server():
    """重启Django服务器"""
    print("重启服务器...")
    stop_server()
    time.sleep(1)  # 等待端口释放
    start_server()

def server_status():
    """查询服务器状态"""
    pid = get_server_pid()
    if pid:
        try:
            process = psutil.Process(pid)
            # 获取进程信息
            create_time = time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(process.create_time()))
            cpu_percent = process.cpu_percent(interval=0.1)
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / (1024 * 1024)
            
            # 获取端口信息
            connections = process.connections()
            ports = []
            for conn in connections:
                if conn.status == 'LISTEN':
                    ports.append(str(conn.laddr.port))
            
            print(f"服务器状态: 运行中")
            print(f"进程ID: {pid}")
            print(f"启动时间: {create_time}")
            print(f"监听端口: {', '.join(ports) if ports else '未知'}")
            print(f"CPU使用率: {cpu_percent:.1f}%")
            print(f"内存使用: {memory_mb:.1f} MB")
        except psutil.NoSuchProcess:
            print("服务器状态: 异常（PID文件存在但进程已终止）")
            remove_pid_file()
    else:
        print("服务器状态: 未运行")

if __name__ == '__main__':
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Django服务器管理工具')
    parser.add_argument('action', choices=['start', 'stop', 'restart', 'status'],
                        help='要执行的操作')
    args = parser.parse_args()
    
    # 执行对应操作
    if args.action == 'start':
        start_server()
    elif args.action == 'stop':
        stop_server()
    elif args.action == 'restart':
        restart_server()
    elif args.action == 'status':
        server_status() 