"""
全局配置文件
"""

# API版本
API_VERSION = 'v1'

# API基础URL
API_BASE_URL = f'/api/{API_VERSION}'

# 服务器配置
SERVER_CONFIG = {
    'host': '0.0.0.0',
    'port': 8000,
    'debug': True
}

# 数据库配置
DATABASE_CONFIG = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': 'db.sqlite3',
    }
}

# JWT配置
JWT_CONFIG = {
    'ACCESS_TOKEN_LIFETIME': 60 * 60 * 24,  # 1天
    'REFRESH_TOKEN_LIFETIME': 60 * 60 * 24 * 7,  # 7天
}

# 用户角色配置
USER_ROLES = {
    'admin': '管理员',
    'staff': '员工',
    'guest': '访客'
}

# 用户状态配置
USER_STATUS = {
    'pending': '待审核',
    'approved': '已审核',
    'rejected': '已拒绝'
}

# 验证码配置
CAPTCHA_CONFIG = {
    'length': 4,
    'timeout': 5 * 60  # 5分钟
}

# CORS配置
CORS_CONFIG = {
    'ALLOW_ALL_ORIGINS': True,
    'ALLOW_CREDENTIALS': True,
    'ALLOWED_ORIGINS': [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
    ]
} 