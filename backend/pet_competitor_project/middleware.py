import json
import logging
from django.http import HttpResponse

logger = logging.getLogger(__name__)

class RequestLogMiddleware:
    """
    中间件，用于记录请求和响应信息，帮助调试
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        
    def __call__(self, request):
        # 请求前处理
        method = request.method
        path = request.path
        content_type = request.content_type
        
        # 记录请求体 (如果是POST/PUT等)
        body = None
        if method in ('POST', 'PUT', 'PATCH'):
            try:
                if request.content_type and 'application/json' in request.content_type:
                    body = json.loads(request.body)
                elif request.content_type and 'multipart/form-data' in request.content_type:
                    body = "Multipart form data, not logged"
                else:
                    body = request.POST.dict()
            except Exception as e:
                body = f"Error parsing body: {str(e)}"
                
        # 记录请求头
        headers = {}
        for key, value in request.headers.items():
            # 不记录敏感头部
            if key.lower() not in ('cookie', 'authorization'):
                headers[key] = value
            else:
                headers[key] = "[REDACTED]"
        
        # 记录请求信息
        request_info = {
            'method': method,
            'path': path,
            'content_type': content_type,
            'headers': headers,
            'body': body
        }
        
        print(f"[API请求] {json.dumps(request_info, ensure_ascii=False, indent=2)}")
        
        # 获取响应
        response = self.get_response(request)
        
        # 请求后处理
        status_code = response.status_code
        
        # 记录响应信息
        response_info = {
            'status_code': status_code,
            'path': path,
            'method': method
        }
        
        # 尝试记录响应内容
        if hasattr(response, 'content'):
            try:
                if response['Content-Type'] == 'application/json':
                    response_body = json.loads(response.content.decode('utf-8'))
                    response_info['body'] = response_body
            except Exception:
                pass
        
        print(f"[API响应] {json.dumps(response_info, ensure_ascii=False, indent=2)}")
        
        return response