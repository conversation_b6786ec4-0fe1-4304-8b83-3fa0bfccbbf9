#!/usr/bin/env python
"""
启动Django服务器的脚本
"""
import os
import sys
import socket
import django
import subprocess
from pet_competitor_project.config import SERVER_CONFIG

def is_port_available(port):
    """检查端口是否可用"""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex(('localhost', port)) != 0

def find_available_port(start_port=8000, max_attempts=10):
    """查找可用端口"""
    port = start_port
    for _ in range(max_attempts):
        if is_port_available(port):
            return port
        port += 1
    raise RuntimeError(f"无法找到可用端口（尝试范围：{start_port}-{start_port+max_attempts-1}）")

def start_server():
    """启动Django服务器"""
    # 设置Django环境
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pet_competitor_project.settings')
    
    # 获取服务器配置
    host = SERVER_CONFIG.get('host', '0.0.0.0')
    port = SERVER_CONFIG.get('port', 8000)
    
    # 检查端口是否可用，如果不可用则寻找替代端口
    if not is_port_available(port):
        print(f"警告：端口 {port} 已被占用")
        port = find_available_port(port)
        print(f"使用替代端口: {port}")
    
    # 构建启动命令
    cmd = [
        sys.executable, 'manage.py', 'runserver',
        f"{host}:{port}"
    ]
    
    # 如果配置了调试模式，添加相应参数
    if SERVER_CONFIG.get('debug', False):
        cmd.append('--noreload')
    
    print(f"启动服务器: {' '.join(cmd)}")
    
    # 启动服务器
    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print("\n服务器已停止")

if __name__ == '__main__':
    start_server() 