# Generated by Django 5.2.1 on 2025-05-12 06:51

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='VerificationCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=6, verbose_name='验证码')),
                ('email', models.EmailField(max_length=254, verbose_name='邮箱')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('is_used', models.BooleanField(default=False, verbose_name='是否已使用')),
            ],
            options={
                'verbose_name': '验证码',
                'verbose_name_plural': '验证码',
            },
        ),
        migrations.CreateModel(
            name='UserProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='手机号码')),
                ('department', models.CharField(blank=True, max_length=100, null=True, verbose_name='部门')),
                ('position', models.CharField(blank=True, max_length=100, null=True, verbose_name='职位')),
                ('status', models.CharField(choices=[('pending', '待审核'), ('approved', '已批准'), ('rejected', '已拒绝')], default='pending', max_length=10, verbose_name='审核状态')),
                ('role', models.CharField(choices=[('admin', '管理员'), ('staff', '员工'), ('user', '普通用户')], default='user', max_length=10, verbose_name='用户角色')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='注册时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('approved_at', models.DateTimeField(blank=True, null=True, verbose_name='审核时间')),
                ('registration_reason', models.TextField(blank=True, null=True, verbose_name='注册原因')),
                ('approved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='approved_users', to=settings.AUTH_USER_MODEL, verbose_name='审核人')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': '用户资料',
                'verbose_name_plural': '用户资料',
            },
        ),
    ]
