from rest_framework import permissions
from .models import UserProfile

class IsAdminUser(permissions.BasePermission):
    """
    只允许管理员用户访问
    """
    def has_permission(self, request, view):
        # 检查是否已认证且是管理员
        if not request.user:
            print("IsAdminUser: 请求没有用户")
            return False
            
        if not request.user.is_authenticated:
            print(f"IsAdminUser: 用户 {request.user} 未认证")
            return False
        
        try:
            profile = UserProfile.objects.get(user=request.user)
            is_admin = profile.role == 'admin'
            
            print(f"IsAdminUser: 用户 {request.user.username} 角色 {profile.role}, 状态 {profile.status}")
            print(f"IsAdminUser: 权限检查结果: {is_admin}")
            
            # 还可以接受Django超级用户
            if request.user.is_superuser:
                print(f"IsAdminUser: 用户 {request.user.username} 是Django超级用户，授予权限")
                return True
                
            return is_admin
        except UserProfile.DoesNotExist:
            print(f"IsAdminUser: 用户 {request.user.username} 资料不存在")
            return False
        except Exception as e:
            print(f"IsAdminUser: 权限检查异常: {str(e)}")
            return False

class IsApprovedUser(permissions.BasePermission):
    """
    只允许已获批准的用户访问
    """
    def has_permission(self, request, view):
        # 检查是否已认证且已获批准
        if not request.user or not request.user.is_authenticated:
            return False
        
        try:
            profile = UserProfile.objects.get(user=request.user)
            return profile.status == 'approved'
        except UserProfile.DoesNotExist:
            return False 