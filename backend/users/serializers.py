from rest_framework import serializers
from django.contrib.auth.models import User
from .models import UserProfile
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.utils import timezone
from captcha.fields import Captcha<PERSON>ield
from captcha.models import CaptchaStore

class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['phone_number', 'department', 'position', 'status', 'role', 
                  'created_at', 'approved_at', 'registration_reason']
        read_only_fields = ['status', 'role', 'created_at', 'approved_at']

class UserSerializer(serializers.ModelSerializer):
    profile = UserProfileSerializer(required=False)
    
    class Meta:
        model = User
        fields = ['id', 'username', 'email', 'first_name', 'last_name', 'profile']
        read_only_fields = ['id']

class UserRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.Char<PERSON>ield(write_only=True, required=True, style={'input_type': 'password'})
    password_confirm = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    captcha_key = serializers.CharField(write_only=True, required=True)
    captcha_value = serializers.CharField(write_only=True, required=True)
    
    # 用户额外信息
    phone_number = serializers.CharField(required=False, allow_blank=True)
    department = serializers.CharField(required=False, allow_blank=True)
    position = serializers.CharField(required=False, allow_blank=True)
    registration_reason = serializers.CharField(required=True)
    
    class Meta:
        model = User
        fields = ['username', 'email', 'password', 'password_confirm', 'first_name', 
                  'last_name', 'captcha_key', 'captcha_value', 'phone_number', 'department', 
                  'position', 'registration_reason']
    
    def validate(self, data):
        print(f"验证数据: {data}")
        
        # 验证密码匹配
        if data['password'] != data['password_confirm']:
            raise serializers.ValidationError({"password_confirm": "密码不匹配"})
        
        # 验证密码复杂度
        try:
            validate_password(data['password'])
        except ValidationError as e:
            raise serializers.ValidationError({"password": e.messages})
        
        # 验证验证码
        captcha_key = data.get('captcha_key')
        captcha_value = data.get('captcha_value')
        
        if not captcha_key or not captcha_value:
            raise serializers.ValidationError({"captcha": "验证码信息不完整"})
            
        try:
            print(f"验证码验证: key={captcha_key}, value={captcha_value}")
            captcha_obj = CaptchaStore.objects.filter(
                hashkey=captcha_key,
                expiration__gt=timezone.now()
            ).first()
            
            if not captcha_obj:
                print("验证码不存在或已过期")
                raise serializers.ValidationError({"captcha": "验证码不存在或已过期"})
                
            # 比较响应（不区分大小写）
            print(f"期望的验证码: {captcha_obj.response}, 提供的验证码: {captcha_value.lower()}")
            if captcha_obj.response != captcha_value.lower():
                print("验证码不匹配")
                raise serializers.ValidationError({"captcha": "验证码错误"})
            
            # 标记验证码已使用并删除
            captcha_obj.delete()
            
        except CaptchaStore.DoesNotExist:
            print("验证码不存在")
            raise serializers.ValidationError({"captcha": "验证码无效或已过期"})
        except Exception as e:
            print(f"验证码验证异常: {str(e)}")
            raise serializers.ValidationError({"captcha": f"验证码验证失败: {str(e)}"})
        
        # 移除captcha_key和captcha_value，避免传递给create方法
        if 'captcha_key' in data:
            data.pop('captcha_key')
        if 'captcha_value' in data:
            data.pop('captcha_value')
            
        return data
    
    def create(self, validated_data):
        # 提取用户资料数据
        profile_data = {}
        profile_fields = ['phone_number', 'department', 'position', 'registration_reason']
        for field in profile_fields:
            if field in validated_data:
                profile_data[field] = validated_data.pop(field)
        
        # 创建用户
        user = User.objects.create(
            username=validated_data['username'],
            email=validated_data['email'],
            first_name=validated_data.get('first_name', ''),
            last_name=validated_data.get('last_name', '')
        )
        user.set_password(validated_data['password'])
        user.is_active = False  # 默认未激活，等待管理员审核
        user.save()
        
        # 创建用户资料
        UserProfile.objects.create(user=user, **profile_data)
        
        return user

class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(required=True, style={'input_type': 'password'})
    captcha_key = serializers.CharField(required=True)
    captcha_value = serializers.CharField(required=True)
    
    def validate(self, data):
        # 验证验证码
        captcha_key = data.get('captcha_key')
        captcha_value = data.get('captcha_value')
        
        try:
            CaptchaStore.objects.get(
                hashkey=captcha_key,
                response=captcha_value.lower(),
                expiration__gt=timezone.now()
            ).delete()
        except CaptchaStore.DoesNotExist:
            raise serializers.ValidationError({"captcha": "验证码无效或已过期"})
        
        return data

class CaptchaSerializer(serializers.Serializer):
    """用于获取验证码"""
    key = serializers.CharField(read_only=True)
    image_url = serializers.CharField(read_only=True)

class UserApprovalSerializer(serializers.Serializer):
    user_id = serializers.IntegerField(required=True)
    status = serializers.ChoiceField(choices=['approved', 'rejected'], required=True)
    
    def validate_user_id(self, value):
        try:
            user = User.objects.get(id=value)
            profile = UserProfile.objects.get(user=user)
            if profile.status != 'pending':
                raise serializers.ValidationError("此用户已经被审核过")
        except (User.DoesNotExist, UserProfile.DoesNotExist):
            raise serializers.ValidationError("用户不存在")
        return value 

class UserPasswordChangeSerializer(serializers.Serializer):
    """用于管理员修改用户密码"""
    new_password = serializers.CharField(required=True, style={'input_type': 'password'})
    confirm_password = serializers.CharField(required=True, style={'input_type': 'password'})
    
    def validate(self, data):
        # 验证密码匹配
        if data['new_password'] != data['confirm_password']:
            raise serializers.ValidationError({"confirm_password": "密码不匹配"})
        
        # 验证密码复杂度
        try:
            validate_password(data['new_password'])
        except ValidationError as e:
            raise serializers.ValidationError({"new_password": e.messages})
            
        return data

class UserRoleChangeSerializer(serializers.Serializer):
    """用于管理员修改用户角色"""
    role = serializers.ChoiceField(choices=['admin', 'staff', 'guest'], required=True)
    
    def validate_role(self, value):
        # 可以在这里添加额外的角色验证逻辑
        return value 