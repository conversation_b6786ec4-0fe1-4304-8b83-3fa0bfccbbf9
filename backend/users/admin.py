from django.contrib import admin
from .models import UserProfile

@admin.register(UserProfile)
class UserProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'status', 'role', 'created_at', 'approved_at')
    list_filter = ('status', 'role')
    search_fields = ('user__username', 'user__email')
    readonly_fields = ('created_at', 'updated_at', 'approved_at')
    fieldsets = (
        ('用户信息', {
            'fields': ('user', 'phone_number', 'department', 'position')
        }),
        ('状态信息', {
            'fields': ('status', 'role', 'created_at', 'updated_at', 'approved_at', 'approved_by')
        }),
        ('注册信息', {
            'fields': ('registration_reason',)
        }),
    )
