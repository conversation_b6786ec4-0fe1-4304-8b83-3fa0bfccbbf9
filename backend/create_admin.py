import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'pet_competitor_project.settings')
django.setup()

from django.contrib.auth.models import User
from users.models import UserProfile
from django.utils import timezone

def create_admin_user():
    """创建初始管理员账户"""
    # 检查是否已存在admin用户
    if User.objects.filter(username='admin').exists():
        print("管理员账户已存在")
        return
    
    # 创建admin用户
    admin_user = User.objects.create_user(
        username='admin',
        email='<EMAIL>',
        first_name='管理员',
        last_name='用户',
        is_active=True,
        is_staff=True,
        is_superuser=True
    )
    admin_user.set_password('admin123')
    admin_user.save()
    
    # 创建admin用户资料
    UserProfile.objects.create(
        user=admin_user,
        role='admin',
        status='approved',
        approved_at=timezone.now(),
        registration_reason='初始管理员账户'
    )
    
    print("管理员账户创建成功")

if __name__ == '__main__':
    create_admin_user()