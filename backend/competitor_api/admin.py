from django.contrib import admin
from .models import (
    CompetitorStore, ServiceOffered, ProductOffered, BrandInfo,
    PricingStrategy, BenchmarkProduct, StoreEnvironment,
    MarketingPromotion, StaffOperations, ComprehensiveRating,
    CompetitorActivity
)

@admin.register(CompetitorStore)
class CompetitorStoreAdmin(admin.ModelAdmin):
    list_display = ('name', 'brand_name', 'store_type', 'competition_relation', 'business_district', 'created_at')
    list_filter = ('store_type', 'competition_relation', 'business_district')
    search_fields = ('name', 'brand_name', 'address')

@admin.register(ServiceOffered)
class ServiceOfferedAdmin(admin.ModelAdmin):
    list_display = ('store', 'service_category', 'service_name', 'price_range')
    list_filter = ('service_category', 'has_cat_area')
    search_fields = ('service_name', 'service_description')

@admin.register(ProductOffered)
class ProductOfferedAdmin(admin.ModelAdmin):
    list_display = ('store', 'has_special_products', 'product_richness_score')
    list_filter = ('has_special_products',)
    search_fields = ('main_product_categories', 'special_products_description')

@admin.register(BrandInfo)
class BrandInfoAdmin(admin.ModelAdmin):
    list_display = ('product', 'category', 'brand_name', 'positioning')
    list_filter = ('positioning', 'category')
    search_fields = ('brand_name',)

@admin.register(PricingStrategy)
class PricingStrategyAdmin(admin.ModelAdmin):
    list_display = ('store', 'price_level', 'price_transparency', 'has_membership', 'promotion_frequency')
    list_filter = ('price_level', 'has_membership', 'promotion_frequency')

@admin.register(BenchmarkProduct)
class BenchmarkProductAdmin(admin.ModelAdmin):
    list_display = ('pricing', 'product_name', 'store_price', 'competitor_price', 'price_difference')
    search_fields = ('product_name',)

@admin.register(StoreEnvironment)
class StoreEnvironmentAdmin(admin.ModelAdmin):
    list_display = ('store', 'cleanliness', 'smell', 'lighting', 'ventilation')
    list_filter = ('cleanliness', 'smell', 'lighting', 'ventilation')

@admin.register(MarketingPromotion)
class MarketingPromotionAdmin(admin.ModelAdmin):
    list_display = ('store', 'online_activity', 'dianping_score', 'meituan_score')
    list_filter = ('online_activity',)
    search_fields = ('positive_keywords', 'negative_keywords')

@admin.register(StaffOperations)
class StaffOperationsAdmin(admin.ModelAdmin):
    list_display = ('store', 'total_staff', 'beauticians', 'service_attitude', 'appointment_fullness')
    list_filter = ('service_attitude', 'appointment_fullness')

@admin.register(ComprehensiveRating)
class ComprehensiveRatingAdmin(admin.ModelAdmin):
    list_display = ('store', 'product_score', 'service_score', 'environment_score', 'marketing_score', 'operations_score', 'total_score', 'rating_level')
    list_filter = ('rating_level',)
    search_fields = ('overall_evaluation',)

@admin.register(CompetitorActivity)
class CompetitorActivityAdmin(admin.ModelAdmin):
    list_display = ('store', 'title', 'activity_type', 'start_date', 'end_date', 'status', 'impact_level')
    list_filter = ('activity_type', 'status', 'impact_level')
    search_fields = ('title', 'description')
    date_hierarchy = 'start_date'
