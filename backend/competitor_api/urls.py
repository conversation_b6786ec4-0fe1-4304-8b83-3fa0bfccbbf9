from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CompetitorStoreViewSet, ServiceOfferedViewSet, ProductOfferedViewSet, BrandInfoViewSet,
    PricingStrategyViewSet, BenchmarkProductViewSet, StoreEnvironmentViewSet,
    MarketingPromotionViewSet, StaffOperationsViewSet, ComprehensiveRatingViewSet,
    CompetitorActivityViewSet, StoreImageViewSet, ActivityImageViewSet, AnalyticsViewSet
)

router = DefaultRouter()
router.register(r'stores', CompetitorStoreViewSet)
router.register(r'services', ServiceOfferedViewSet)
router.register(r'products', ProductOfferedViewSet)
router.register(r'brands', BrandInfoViewSet)
router.register(r'pricing', PricingStrategyViewSet)
router.register(r'benchmark-products', BenchmarkProductViewSet)
router.register(r'environments', StoreEnvironmentViewSet)
router.register(r'marketing', MarketingPromotionViewSet)
router.register(r'staff', StaffOperationsViewSet)
router.register(r'ratings', ComprehensiveRatingViewSet)
router.register(r'activities', CompetitorActivityViewSet)
router.register(r'store-images', StoreImageViewSet)
router.register(r'activity-images', ActivityImageViewSet)
router.register(r'analytics', AnalyticsViewSet, basename='analytics')

urlpatterns = [
    path('', include(router.urls)),
]