# Generated by Django 5.2.1 on 2025-05-12 04:30

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('competitor_api', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CompetitorActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=200, verbose_name='活动标题')),
                ('activity_type', models.CharField(choices=[('promotion', '促销活动'), ('event', '营销活动/事件'), ('new_product', '新品发布'), ('price_change', '价格变动'), ('service_change', '服务调整'), ('store_change', '门店变动'), ('other', '其他')], max_length=20, verbose_name='活动类型')),
                ('description', models.TextField(verbose_name='活动描述')),
                ('start_date', models.DateField(blank=True, null=True, verbose_name='开始日期')),
                ('end_date', models.DateField(blank=True, null=True, verbose_name='结束日期')),
                ('status', models.CharField(choices=[('upcoming', '即将开始'), ('active', '进行中'), ('ended', '已结束'), ('recurring', '周期性')], max_length=20, verbose_name='活动状态')),
                ('impact_level', models.CharField(blank=True, choices=[('high', '高影响'), ('medium', '中等影响'), ('low', '低影响'), ('unknown', '影响未知')], max_length=20, null=True, verbose_name='影响级别')),
                ('discount_amount', models.CharField(blank=True, max_length=100, null=True, verbose_name='折扣力度')),
                ('target_customers', models.CharField(blank=True, max_length=200, null=True, verbose_name='目标客户群体')),
                ('participation_threshold', models.TextField(blank=True, null=True, verbose_name='参与门槛')),
                ('marketing_channels', models.TextField(blank=True, null=True, verbose_name='营销渠道')),
                ('response_strategy', models.TextField(blank=True, null=True, verbose_name='应对策略')),
                ('results', models.TextField(blank=True, null=True, verbose_name='活动效果')),
                ('photos', models.TextField(blank=True, null=True, verbose_name='活动照片/海报')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activities', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '竞品活动追踪',
                'verbose_name_plural': '竞品活动追踪',
                'ordering': ['-start_date', '-created_at'],
            },
        ),
    ]
