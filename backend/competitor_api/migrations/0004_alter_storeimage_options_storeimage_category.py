# Generated by Django 5.2.1 on 2025-05-13 08:43

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('competitor_api', '0003_competitoractivity_main_image_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='storeimage',
            options={'ordering': ['category', 'order', 'uploaded_at'], 'verbose_name': '门店照片', 'verbose_name_plural': '门店照片'},
        ),
        migrations.AddField(
            model_name='storeimage',
            name='category',
            field=models.CharField(choices=[('storefront', '门店正面/入口'), ('window_display', '橱窗展示'), ('surroundings', '周边环境'), ('business_info', '营业时间/联系方式'), ('retail_overview', '零售区整体布局'), ('food_section', '主粮区'), ('snack_section', '零食区'), ('supplies_section', '用品区'), ('grooming_products', '洗护/医疗保健品区'), ('featured_products', '重点品牌/特色产品'), ('price_tags', '价格标签/促销信息'), ('checkout_area', '收银台区域'), ('grooming_overview', '洗护区全景'), ('grooming_station', '单个洗护工位'), ('waiting_area', '等待区/家长休息区'), ('service_menu', '价目表/服务项目单'), ('grooming_brands', '使用的洗护产品品牌'), ('brand_elements', '品牌元素/文化墙'), ('special_facilities', '特色服务/设施'), ('staff_interaction', '员工互动'), ('cleanliness', '卫生清洁状况'), ('other', '其他')], default='other', max_length=50, verbose_name='图片类别'),
        ),
    ]
