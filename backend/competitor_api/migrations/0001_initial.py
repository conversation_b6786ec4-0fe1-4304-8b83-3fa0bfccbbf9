# Generated by Django 5.2.1 on 2025-05-12 02:00

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='CompetitorStore',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='门店名称')),
                ('brand_name', models.CharField(blank=True, max_length=100, null=True, verbose_name='门店简称/品牌名')),
                ('store_type', models.CharField(choices=[('direct_chain', '直营连锁'), ('franchise_chain', '加盟连锁'), ('single_store', '单体店'), ('pet_hospital', '宠物医院附带'), ('retail_only', '纯零售店')], max_length=20, verbose_name='门店类型')),
                ('competition_relation', models.CharField(choices=[('direct_competitor', '直接竞争对手'), ('indirect_hospital', '间接竞争对手-宠物医院'), ('indirect_retail', '间接竞争对手-纯零售')], max_length=20, verbose_name='竞争关系')),
                ('address', models.TextField(verbose_name='详细地址')),
                ('longitude', models.FloatField(blank=True, null=True, verbose_name='经度')),
                ('latitude', models.FloatField(blank=True, null=True, verbose_name='纬度')),
                ('business_district', models.CharField(blank=True, max_length=100, null=True, verbose_name='所属商圈/区域')),
                ('transportation_convenience', models.TextField(blank=True, null=True, verbose_name='交通便利性')),
                ('visibility', models.CharField(blank=True, choices=[('excellent', '极佳'), ('good', '良好'), ('average', '一般'), ('poor', '较差')], max_length=20, null=True, verbose_name='可见性与易达性')),
                ('surrounding_community_type', models.TextField(blank=True, null=True, verbose_name='周边社区类型')),
                ('phone1', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话1')),
                ('phone2', models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话2')),
                ('wechat_qr', models.TextField(blank=True, null=True, verbose_name='微信客服号/群二维码')),
                ('business_hours_weekday', models.CharField(blank=True, max_length=100, null=True, verbose_name='营业时间(工作日)')),
                ('business_hours_weekend', models.CharField(blank=True, max_length=100, null=True, verbose_name='营业时间(周末/节假日)')),
                ('opening_year', models.IntegerField(blank=True, null=True, verbose_name='开业年份')),
                ('store_area', models.FloatField(blank=True, null=True, verbose_name='门店面积(m²)')),
                ('photos', models.TextField(blank=True, null=True, verbose_name='门店照片')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '竞争对手门店',
                'verbose_name_plural': '竞争对手门店',
            },
        ),
        migrations.CreateModel(
            name='ComprehensiveRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_score', models.FloatField(blank=True, null=True, verbose_name='产品力评分')),
                ('service_score', models.FloatField(blank=True, null=True, verbose_name='服务力评分')),
                ('environment_score', models.FloatField(blank=True, null=True, verbose_name='环境评分')),
                ('marketing_score', models.FloatField(blank=True, null=True, verbose_name='营销力评分')),
                ('operations_score', models.FloatField(blank=True, null=True, verbose_name='运营能力评分')),
                ('total_score', models.FloatField(blank=True, null=True, verbose_name='总分')),
                ('rating_level', models.CharField(blank=True, max_length=50, null=True, verbose_name='评级')),
                ('overall_evaluation', models.TextField(blank=True, null=True, verbose_name='整体评价与关键发现')),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='rating', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '综合评分',
                'verbose_name_plural': '综合评分',
            },
        ),
        migrations.CreateModel(
            name='MarketingPromotion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('website_url', models.URLField(blank=True, null=True, verbose_name='官方网站URL')),
                ('wechat_account', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信公众号名称/ID')),
                ('wechat_miniapp', models.CharField(blank=True, max_length=100, null=True, verbose_name='微信小程序名称')),
                ('xiaohongshu_account', models.CharField(blank=True, max_length=100, null=True, verbose_name='小红书账号')),
                ('douyin_account', models.CharField(blank=True, max_length=100, null=True, verbose_name='抖音账号')),
                ('dianping_url', models.URLField(blank=True, null=True, verbose_name='大众点评店铺链接')),
                ('meituan_url', models.URLField(blank=True, null=True, verbose_name='美团店铺链接')),
                ('online_activity', models.CharField(blank=True, choices=[('very_active', '非常活跃'), ('active', '活跃'), ('average', '一般'), ('inactive', '不活跃'), ('none', '基本无')], max_length=20, null=True, verbose_name='线上活跃度评估')),
                ('dianping_score', models.FloatField(blank=True, null=True, verbose_name='大众点评评分')),
                ('dianping_reviews', models.IntegerField(blank=True, null=True, verbose_name='大众点评评论数')),
                ('meituan_score', models.FloatField(blank=True, null=True, verbose_name='美团评分')),
                ('meituan_reviews', models.IntegerField(blank=True, null=True, verbose_name='美团评论数')),
                ('positive_keywords', models.TextField(blank=True, null=True, verbose_name='主要好评关键词')),
                ('negative_keywords', models.TextField(blank=True, null=True, verbose_name='主要差评关键词')),
                ('offline_marketing', models.TextField(blank=True, null=True, verbose_name='线下营销活动记录')),
                ('brand_perception', models.TextField(blank=True, null=True, verbose_name='品牌形象感知')),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='marketing', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '营销与口碑',
                'verbose_name_plural': '营销与口碑',
            },
        ),
        migrations.CreateModel(
            name='PricingStrategy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('price_level', models.CharField(blank=True, choices=[('high_end', '高端'), ('mid_high', '中高端'), ('mid_range', '中端'), ('mid_low', '中低端'), ('economy', '经济型')], max_length=20, null=True, verbose_name='整体价格定位')),
                ('price_transparency', models.CharField(blank=True, choices=[('transparent', '公开透明'), ('partially', '部分公开'), ('inquiry', '需咨询')], max_length=20, null=True, verbose_name='价格透明度')),
                ('has_membership', models.BooleanField(default=False, verbose_name='是否有会员制度')),
                ('membership_types', models.TextField(blank=True, null=True, verbose_name='会员卡类型')),
                ('membership_threshold', models.TextField(blank=True, null=True, verbose_name='办理门槛/费用')),
                ('membership_benefits', models.TextField(blank=True, null=True, verbose_name='会员主要权益')),
                ('membership_attractiveness', models.IntegerField(blank=True, null=True, verbose_name='会员体系吸引力评估')),
                ('promotion_types', models.TextField(blank=True, null=True, verbose_name='常规促销活动类型')),
                ('promotion_frequency', models.CharField(blank=True, choices=[('frequent', '频繁'), ('quite_often', '较多'), ('average', '一般'), ('rare', '较少'), ('never', '几乎没有')], max_length=20, null=True, verbose_name='促销活动频率')),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pricing', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '价格与会员体系',
                'verbose_name_plural': '价格与会员体系',
            },
        ),
        migrations.CreateModel(
            name='BenchmarkProduct',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('product_name', models.CharField(max_length=100, verbose_name='商品名称')),
                ('store_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='本店售价')),
                ('competitor_price', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='竞对售价')),
                ('price_difference', models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True, verbose_name='差价')),
                ('pricing', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='benchmark_products', to='competitor_api.pricingstrategy', verbose_name='价格策略')),
            ],
            options={
                'verbose_name': '标杆商品比价',
                'verbose_name_plural': '标杆商品比价',
            },
        ),
        migrations.CreateModel(
            name='ProductOffered',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('main_product_categories', models.TextField(blank=True, null=True, verbose_name='主营产品大类')),
                ('product_richness_score', models.IntegerField(blank=True, null=True, verbose_name='产品丰富度评分')),
                ('product_display_notes', models.TextField(blank=True, null=True, verbose_name='产品陈列与布局评价')),
                ('has_special_products', models.BooleanField(default=False, verbose_name='是否有特色/独家产品')),
                ('special_products_description', models.TextField(blank=True, null=True, verbose_name='特色产品描述')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='products', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '零售产品',
                'verbose_name_plural': '零售产品',
            },
        ),
        migrations.CreateModel(
            name='BrandInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category', models.CharField(max_length=50, verbose_name='品类')),
                ('brand_name', models.CharField(max_length=100, verbose_name='品牌名称')),
                ('positioning', models.CharField(blank=True, choices=[('high_end', '高端'), ('mid_range', '中端'), ('economy', '大众/经济')], max_length=20, null=True, verbose_name='品牌定位')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注/特色')),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='brands', to='competitor_api.productoffered', verbose_name='产品信息')),
            ],
            options={
                'verbose_name': '品牌信息',
                'verbose_name_plural': '品牌信息',
            },
        ),
        migrations.CreateModel(
            name='ServiceOffered',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_category', models.CharField(max_length=100, verbose_name='服务大类')),
                ('service_name', models.CharField(max_length=100, verbose_name='服务名称')),
                ('service_description', models.TextField(blank=True, null=True, verbose_name='服务内容描述')),
                ('price_range', models.CharField(blank=True, max_length=100, null=True, verbose_name='价格/价格范围')),
                ('member_price', models.CharField(blank=True, max_length=100, null=True, verbose_name='会员价/套餐价')),
                ('product_brand', models.CharField(blank=True, max_length=100, null=True, verbose_name='使用产品品牌')),
                ('has_cat_area', models.BooleanField(default=False, verbose_name='是否有独立猫咪洗护区')),
                ('notes', models.TextField(blank=True, null=True, verbose_name='备注')),
                ('appointment_methods', models.CharField(blank=True, max_length=255, null=True, verbose_name='预约方式')),
                ('waiting_area_facilities', models.TextField(blank=True, null=True, verbose_name='等待区设施描述')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='services', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '服务项目',
                'verbose_name_plural': '服务项目',
            },
        ),
        migrations.CreateModel(
            name='StaffOperations',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_staff', models.IntegerField(blank=True, null=True, verbose_name='预估员工总数')),
                ('beauticians', models.IntegerField(blank=True, null=True, verbose_name='美容师数量')),
                ('service_attitude', models.IntegerField(blank=True, choices=[(5, '非常好'), (4, '好'), (3, '一般'), (2, '较差'), (1, '差')], null=True, verbose_name='员工服务态度')),
                ('professional_knowledge', models.IntegerField(blank=True, null=True, verbose_name='员工专业知识水平')),
                ('customer_flow', models.TextField(blank=True, null=True, verbose_name='客流量观察')),
                ('appointment_fullness', models.CharField(blank=True, choices=[('very_difficult', '很难约'), ('days_ahead', '需提前几天'), ('easy', '较容易约'), ('anytime', '随时可约')], max_length=20, null=True, verbose_name='预约饱满度')),
                ('operational_efficiency', models.IntegerField(blank=True, null=True, verbose_name='运营效率感知')),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='staff', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '人员与运营',
                'verbose_name_plural': '人员与运营',
            },
        ),
        migrations.CreateModel(
            name='StoreEnvironment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('cleanliness', models.IntegerField(blank=True, choices=[(5, '非常干净'), (4, '干净'), (3, '一般'), (2, '较差'), (1, '差')], null=True, verbose_name='门店清洁度')),
                ('smell', models.CharField(blank=True, choices=[('fresh', '清新无异味'), ('light', '轻微宠物味'), ('moderate', '较重宠物味'), ('unpleasant', '难闻')], max_length=20, null=True, verbose_name='店内气味')),
                ('decoration_style', models.CharField(blank=True, max_length=100, null=True, verbose_name='装修风格')),
                ('layout_rationality', models.IntegerField(blank=True, null=True, verbose_name='空间布局合理性')),
                ('lighting', models.CharField(blank=True, choices=[('very_bright', '非常明亮'), ('bright', '明亮'), ('average', '一般'), ('dim', '偏暗')], max_length=20, null=True, verbose_name='采光情况')),
                ('ventilation', models.CharField(blank=True, choices=[('good', '良好'), ('average', '一般'), ('poor', '较差')], max_length=20, null=True, verbose_name='通风情况')),
                ('beauty_equipment', models.TextField(blank=True, null=True, verbose_name='美容区设备情况')),
                ('retail_display', models.TextField(blank=True, null=True, verbose_name='零售区货架与陈列')),
                ('customer_facilities', models.TextField(blank=True, null=True, verbose_name='顾客便利设施')),
                ('store', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='environment', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '门店环境与设施',
                'verbose_name_plural': '门店环境与设施',
            },
        ),
    ]
