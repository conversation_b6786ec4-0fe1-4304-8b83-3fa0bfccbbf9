# Generated by Django 5.2.1 on 2025-05-16 03:38

from django.db import migrations, models


def merge_phone_fields(apps, schema_editor):
    """
    u5c06phone1u548cphone2u5b57u6bb5u7684u6570u636eu5408u5e76u5230u65b0u7684phoneu5b57u6bb5
    u591au4e2au7535u8bddu53f7u7801u7528u5206u53f7u5206u9694
    """
    CompetitorStore = apps.get_model('competitor_api', 'CompetitorStore')
    for store in CompetitorStore.objects.all():
        # u6536u96c6u6240u6709u975eu7a7au7535u8bddu53f7u7801
        phone_numbers = []
        if store.phone1 and store.phone1.strip():
            phone_numbers.append(store.phone1.strip())
        if store.phone2 and store.phone2.strip():
            phone_numbers.append(store.phone2.strip())
        
        # u5982u679cu6709u7535u8bddu53f7u7801uff0cu5408u5e76u5e76u5206u53f7u5206u9694
        if phone_numbers:
            store.phone = ';'.join(phone_numbers)
            store.save()


class Migration(migrations.Migration):

    dependencies = [
        ('competitor_api', '0004_alter_storeimage_options_storeimage_category'),
    ]

    operations = [
        # u9996u5148u6dfbu52a0u65b0u5b57u6bb5
        migrations.AddField(
            model_name='competitorstore',
            name='phone',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='u8054u7cfbu7535u8bdduff08u591au4e2au53f7u7801u7528u5206u53f7u5206u9694uff09'),
        ),
        
        # u8fd0u884cu6570u636eu8fc1u79fbu4ee3u7801
        migrations.RunPython(merge_phone_fields),
        
        # u7136u540eu79fbu9664u65e7u5b57u6bb5
        migrations.RemoveField(
            model_name='competitorstore',
            name='phone1',
        ),
        migrations.RemoveField(
            model_name='competitorstore',
            name='phone2',
        ),
    ]
