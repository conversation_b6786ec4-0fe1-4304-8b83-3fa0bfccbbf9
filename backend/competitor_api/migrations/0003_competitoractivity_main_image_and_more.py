# Generated by Django 5.2.1 on 2025-05-13 08:29

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('competitor_api', '0002_competitoractivity'),
    ]

    operations = [
        migrations.AddField(
            model_name='competitoractivity',
            name='main_image',
            field=models.ImageField(blank=True, null=True, upload_to='activity_images/%Y/%m/%d/', verbose_name='活动主图片'),
        ),
        migrations.AddField(
            model_name='competitorstore',
            name='main_image',
            field=models.ImageField(blank=True, null=True, upload_to='store_images/%Y/%m/%d/', verbose_name='门店主照片'),
        ),
        migrations.AlterField(
            model_name='competitoractivity',
            name='photos',
            field=models.TextField(blank=True, null=True, verbose_name='活动照片/海报(旧)'),
        ),
        migrations.AlterField(
            model_name='competitorstore',
            name='photos',
            field=models.TextField(blank=True, null=True, verbose_name='门店照片(旧)'),
        ),
        migrations.CreateModel(
            name='ActivityImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='activity_images/%Y/%m/%d/', verbose_name='活动照片')),
                ('caption', models.CharField(blank=True, max_length=200, null=True, verbose_name='图片说明')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('activity', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='activity_images', to='competitor_api.competitoractivity', verbose_name='活动')),
            ],
            options={
                'verbose_name': '活动照片',
                'verbose_name_plural': '活动照片',
                'ordering': ['order', 'uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='StoreImage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('image', models.ImageField(upload_to='store_images/%Y/%m/%d/', verbose_name='门店照片')),
                ('caption', models.CharField(blank=True, max_length=200, null=True, verbose_name='图片说明')),
                ('order', models.PositiveIntegerField(default=0, verbose_name='排序')),
                ('uploaded_at', models.DateTimeField(auto_now_add=True, verbose_name='上传时间')),
                ('store', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='store_images', to='competitor_api.competitorstore', verbose_name='门店')),
            ],
            options={
                'verbose_name': '门店照片',
                'verbose_name_plural': '门店照片',
                'ordering': ['order', 'uploaded_at'],
            },
        ),
    ]
