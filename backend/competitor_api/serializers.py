from rest_framework import serializers
from .models import (
    CompetitorStore, ServiceOffered, ProductOffered, BrandInfo,
    PricingStrategy, BenchmarkProduct, StoreEnvironment,
    MarketingPromotion, StaffOperations, ComprehensiveRating,
    CompetitorActivity, StoreImage, ActivityImage
)

class ServiceOfferedSerializer(serializers.ModelSerializer):
    class Meta:
        model = ServiceOffered
        fields = '__all__'

class BrandInfoSerializer(serializers.ModelSerializer):
    class Meta:
        model = BrandInfo
        fields = '__all__'

class ProductOfferedSerializer(serializers.ModelSerializer):
    brands = BrandInfoSerializer(many=True, read_only=True)
    
    class Meta:
        model = ProductOffered
        fields = '__all__'

class BenchmarkProductSerializer(serializers.ModelSerializer):
    class Meta:
        model = BenchmarkProduct
        fields = '__all__'

class PricingStrategySerializer(serializers.ModelSerializer):
    benchmark_products = BenchmarkProductSerializer(many=True, read_only=True)
    
    class Meta:
        model = PricingStrategy
        fields = '__all__'

class StoreEnvironmentSerializer(serializers.ModelSerializer):
    class Meta:
        model = StoreEnvironment
        fields = '__all__'

class MarketingPromotionSerializer(serializers.ModelSerializer):
    class Meta:
        model = MarketingPromotion
        fields = '__all__'

class StaffOperationsSerializer(serializers.ModelSerializer):
    class Meta:
        model = StaffOperations
        fields = '__all__'

class ComprehensiveRatingSerializer(serializers.ModelSerializer):
    class Meta:
        model = ComprehensiveRating
        fields = '__all__'

# 新增门店图片序列化器
class StoreImageSerializer(serializers.ModelSerializer):
    category_display = serializers.CharField(source='get_category_display', read_only=True)
    
    class Meta:
        model = StoreImage
        fields = ['id', 'store', 'image', 'caption', 'category', 'category_display', 'order', 'uploaded_at']

# 新增活动图片序列化器
class ActivityImageSerializer(serializers.ModelSerializer):
    class Meta:
        model = ActivityImage
        fields = ['id', 'activity', 'image', 'caption', 'order', 'uploaded_at']

class CompetitorActivitySerializer(serializers.ModelSerializer):
    store_name = serializers.ReadOnlyField(source='store.name')
    activity_images = ActivityImageSerializer(many=True, read_only=True)
    
    class Meta:
        model = CompetitorActivity
        fields = '__all__'

class CompetitorStoreSerializer(serializers.ModelSerializer):
    services = ServiceOfferedSerializer(many=True, read_only=True)
    products = ProductOfferedSerializer(many=True, read_only=True)
    pricing = PricingStrategySerializer(read_only=True)
    environment = StoreEnvironmentSerializer(read_only=True)
    marketing = MarketingPromotionSerializer(read_only=True)
    staff = StaffOperationsSerializer(read_only=True)
    rating = ComprehensiveRatingSerializer(read_only=True)
    activities = CompetitorActivitySerializer(many=True, read_only=True)
    store_images = StoreImageSerializer(many=True, read_only=True)
    
    # 自定义电话字段，确保能够处理空值和null值
    phone = serializers.CharField(allow_blank=True, required=False, allow_null=True)
    
    # 使用ListField来接收多选的主营业务数据
    main_business_list = serializers.ListField(
        child=serializers.CharField(),
        required=False,
        write_only=True
    )
    
    def validate_phone(self, value):
        """
        自定义电话字段验证，确保能够处理各种类型的输入
        """
        if value is None:
            return None
            
        if isinstance(value, list):
            # 如果是列表，将其转换为分号分隔的字符串
            return ';'.join(filter(None, [str(p).strip() if p else '' for p in value]))
            
        if not isinstance(value, str):
            # 如果不是字符串，尝试转换为字符串
            try:
                value = str(value)
            except:
                return None
                
        # 返回去除前后空格的字符串，如果为空则返回None
        return value.strip() if value.strip() else None
    
    def to_internal_value(self, data):
        """
        将前端传入的主营业务数组转换为后端期望的分号分隔字符串
        """
        # 检查是否有主营业务数组数据
        main_business_data = data.get('main_business')
        
        # 如果main_business是数组，将其转移到main_business_list字段
        if isinstance(main_business_data, list):
            data = data.copy()  # 创建数据的副本以避免修改原始数据
            data['main_business_list'] = main_business_data
            # 移除原始的main_business字段，避免冲突
            data.pop('main_business')
        
        return super().to_internal_value(data)
    
    def create(self, validated_data):
        """
        创建门店时，处理主营业务数据
        """
        # 处理主营业务列表
        main_business_list = validated_data.pop('main_business_list', None)
        if main_business_list:
            # 将列表转换为分号分隔的字符串
            validated_data['main_business'] = ';'.join(filter(None, main_business_list))
        
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        """
        更新门店时，处理主营业务数据
        """
        # 处理主营业务列表
        main_business_list = validated_data.pop('main_business_list', None)
        if main_business_list is not None:  # 显式检查None以处理空列表情况
            # 将列表转换为分号分隔的字符串
            validated_data['main_business'] = ';'.join(filter(None, main_business_list))
        
        return super().update(instance, validated_data)
    
    class Meta:
        model = CompetitorStore
        fields = '__all__'

class CompetitorStoreListSerializer(serializers.ModelSerializer):
    """A simplified serializer for list views"""
    # 添加距离字段（仅当使用地图搜索时有值）
    distance_km = serializers.SerializerMethodField()
    
    def get_distance_km(self, obj):
        # 如果对象有distance_km属性（由地图搜索添加），则返回该值
        return getattr(obj, 'distance_km', None)
    
    class Meta:
        model = CompetitorStore
        fields = ['id', 'name', 'brand_name', 'store_type', 'competition_relation', 
                 'address', 'business_district', 'opening_year', 'created_at', 'updated_at', 
                 'main_image', 'longitude', 'latitude', 'distance_km', 'phone', 'main_business'] 