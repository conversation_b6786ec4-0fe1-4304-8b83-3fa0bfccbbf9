"""
高德POI数据自动补充系统
用于从高德地图POI数据中提取和补充门店信息
"""

import re
import json
from typing import Dict, List, Optional, Tuple
from datetime import datetime


class AmapDataEnricher:
    """高德POI数据增强器"""
    
    # POI类型编码映射
    POI_TYPE_MAPPING = {
        '061211': {  # 宠物服务
            'store_type': 'single_store',
            'competition_relation': 'direct_competitor',
            'main_business': ['pet_grooming', 'pet_beauty'],
            'priority': 'high'
        },
        '090701': {  # 宠物医院
            'store_type': 'pet_hospital',
            'competition_relation': 'indirect_hospital',
            'main_business': ['pet_diagnosis'],
            'priority': 'medium'
        },
        '061212': {  # 宠物用品店
            'store_type': 'retail_only',
            'competition_relation': 'indirect_retail',
            'main_business': ['pet_supplies'],
            'priority': 'medium'
        }
    }
    
    # 营业时间格式映射
    BUSINESS_HOURS_PATTERNS = [
        r'(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})',  # 09:00-21:00
        r'(\d{1,2})点-(\d{1,2})点',               # 9点-21点
        r'(\d{1,2}):(\d{2})～(\d{1,2}):(\d{2})', # 09:00～21:00
    ]
    
    # 品牌名称提取规则
    BRAND_EXTRACTION_RULES = [
        r'^([^(（]+)[（(]',  # 提取括号前的内容
        r'([A-Za-z\u4e00-\u9fa5]+)宠物',  # 提取"宠物"前的内容
        r'([A-Za-z\u4e00-\u9fa5]+)动物医院',  # 提取"动物医院"前的内容
        r'^([A-Za-z\u4e00-\u9fa5]{2,8})',  # 提取前2-8个字符作为品牌
    ]
    
    def __init__(self):
        self.enrichment_stats = {
            'total_processed': 0,
            'successfully_enriched': 0,
            'failed_enrichments': 0,
            'field_completion_rates': {}
        }
    
    def enrich_store_data(self, poi_data: Dict) -> Dict:
        """
        从高德POI数据中提取并补充门店信息
        
        Args:
            poi_data: 高德POI原始数据
            
        Returns:
            Dict: 补充后的门店数据
        """
        self.enrichment_stats['total_processed'] += 1
        
        try:
            enriched_data = self._extract_basic_info(poi_data)
            enriched_data.update(self._extract_business_info(poi_data))
            enriched_data.update(self._extract_contact_info(poi_data))
            enriched_data.update(self._extract_location_info(poi_data))
            enriched_data.update(self._extract_business_hours(poi_data))
            enriched_data.update(self._extract_rating_info(poi_data))
            enriched_data.update(self._extract_additional_info(poi_data))
            
            # 添加数据来源标记
            enriched_data['data_source'] = 'amap_poi'
            enriched_data['amap_poi_id'] = poi_data.get('id')
            enriched_data['enrichment_timestamp'] = datetime.now().isoformat()
            
            self.enrichment_stats['successfully_enriched'] += 1
            return enriched_data
            
        except Exception as e:
            self.enrichment_stats['failed_enrichments'] += 1
            print(f"数据补充失败: {e}")
            return {}
    
    def _extract_basic_info(self, poi_data: Dict) -> Dict:
        """提取基本信息"""
        data = {}
        
        # 门店名称
        if poi_data.get('name'):
            data['name'] = poi_data['name'].strip()
            
            # 提取品牌名称
            brand_name = self._extract_brand_name(poi_data['name'])
            if brand_name:
                data['brand_name'] = brand_name
        
        # 详细地址
        if poi_data.get('address'):
            data['address'] = poi_data['address'].strip()
            
            # 从地址中提取商圈信息
            business_district = self._extract_business_district(poi_data['address'])
            if business_district:
                data['business_district'] = business_district
        
        return data
    
    def _extract_business_info(self, poi_data: Dict) -> Dict:
        """提取业务信息"""
        data = {}
        
        typecode = poi_data.get('typecode', '')
        
        # 根据POI类型编码确定业务信息
        for code, mapping in self.POI_TYPE_MAPPING.items():
            if typecode.startswith(code):
                data['store_type'] = mapping['store_type']
                data['competition_relation'] = mapping['competition_relation']
                data['main_business'] = ';'.join(mapping['main_business'])
                break
        else:
            # 默认值
            data['store_type'] = 'single_store'
            data['competition_relation'] = 'direct_competitor'
            data['main_business'] = 'pet_grooming;pet_beauty'
        
        # 从POI类型名称中提取更多业务信息
        poi_type = poi_data.get('type', '')
        if '医院' in poi_type:
            data['store_type'] = 'pet_hospital'
            data['competition_relation'] = 'indirect_hospital'
            data['main_business'] = 'pet_diagnosis'
        elif '用品' in poi_type or '商店' in poi_type:
            data['store_type'] = 'retail_only'
            data['competition_relation'] = 'indirect_retail'
            data['main_business'] = 'pet_supplies'
        
        return data
    
    def _extract_contact_info(self, poi_data: Dict) -> Dict:
        """提取联系信息"""
        data = {}
        
        # 电话号码
        if poi_data.get('tel'):
            phone = self._clean_phone_number(poi_data['tel'])
            if phone:
                data['phone'] = phone
        
        return data
    
    def _extract_location_info(self, poi_data: Dict) -> Dict:
        """提取位置信息"""
        data = {}
        
        # 经纬度坐标
        location = poi_data.get('location', '')
        if location and ',' in location:
            try:
                lng, lat = location.split(',')
                data['longitude'] = float(lng.strip())
                data['latitude'] = float(lat.strip())
            except (ValueError, IndexError):
                pass
        
        # 距离信息（如果有）
        if poi_data.get('distance'):
            try:
                distance_km = float(poi_data['distance']) / 1000
                data['distance_from_center'] = round(distance_km, 2)
            except (ValueError, TypeError):
                pass
        
        return data
    
    def _extract_business_hours(self, poi_data: Dict) -> Dict:
        """提取营业时间"""
        data = {}
        
        biz_ext = poi_data.get('biz_ext', {})
        if isinstance(biz_ext, dict) and biz_ext.get('open_time'):
            open_time = biz_ext['open_time']
            
            # 解析营业时间
            parsed_hours = self._parse_business_hours(open_time)
            if parsed_hours:
                data['business_hours_weekday'] = parsed_hours
                data['business_hours_weekend'] = parsed_hours  # 默认周末与工作日相同
        
        return data
    
    def _extract_rating_info(self, poi_data: Dict) -> Dict:
        """提取评分信息"""
        data = {}
        
        biz_ext = poi_data.get('biz_ext', {})
        if isinstance(biz_ext, dict):
            # 评分信息
            if biz_ext.get('rating'):
                try:
                    rating = float(biz_ext['rating'])
                    if 0 <= rating <= 5:
                        # 映射到营销评分
                        data['initial_rating'] = rating
                        
                        # 根据评分推断服务质量
                        if rating >= 4.5:
                            data['service_quality_estimate'] = 'excellent'
                        elif rating >= 4.0:
                            data['service_quality_estimate'] = 'good'
                        elif rating >= 3.5:
                            data['service_quality_estimate'] = 'average'
                        else:
                            data['service_quality_estimate'] = 'poor'
                except (ValueError, TypeError):
                    pass
            
            # 消费水平
            if biz_ext.get('cost'):
                cost_info = biz_ext['cost']
                price_level = self._parse_price_level(cost_info)
                if price_level:
                    data['estimated_price_level'] = price_level
        
        return data
    
    def _extract_additional_info(self, poi_data: Dict) -> Dict:
        """提取其他补充信息"""
        data = {}
        
        biz_ext = poi_data.get('biz_ext', {})
        if isinstance(biz_ext, dict):
            # 特色标签
            if biz_ext.get('tag'):
                tags = biz_ext['tag']
                if isinstance(tags, str):
                    data['amap_tags'] = tags
                elif isinstance(tags, list):
                    data['amap_tags'] = ';'.join(tags)
        
        # 添加备注信息
        notes = []
        notes.append(f"数据来源: 高德地图POI")
        notes.append(f"POI类型: {poi_data.get('type', '未知')}")
        notes.append(f"类型编码: {poi_data.get('typecode', '未知')}")
        
        if poi_data.get('distance'):
            distance_km = round(float(poi_data['distance']) / 1000, 2)
            notes.append(f"距离搜索中心: {distance_km}公里")
        
        data['notes'] = '\n'.join(notes)
        
        return data
    
    def _extract_brand_name(self, store_name: str) -> Optional[str]:
        """从门店名称中提取品牌名称"""
        if not store_name:
            return None
        
        for pattern in self.BRAND_EXTRACTION_RULES:
            match = re.search(pattern, store_name)
            if match:
                brand = match.group(1).strip()
                if len(brand) >= 2 and len(brand) <= 10:
                    return brand
        
        return None
    
    def _extract_business_district(self, address: str) -> Optional[str]:
        """从地址中提取商圈信息"""
        if not address:
            return None
        
        # 提取区域信息的正则表达式
        patterns = [
            r'([^市]+市)([^区县]+[区县])',  # 提取市区信息
            r'([^区县]+[区县])([^街道路]+[街道路])',  # 提取区街道信息
            r'([^街道路]+[街道路])',  # 提取街道信息
        ]
        
        for pattern in patterns:
            match = re.search(pattern, address)
            if match:
                if len(match.groups()) >= 2:
                    return match.group(2)
                else:
                    return match.group(1)
        
        return None
    
    def _clean_phone_number(self, phone: str) -> Optional[str]:
        """清理电话号码格式"""
        if not phone:
            return None
        
        # 移除非数字字符，保留分隔符
        cleaned = re.sub(r'[^\d\-;,，；]', '', phone)
        
        # 验证电话号码格式
        if re.match(r'^[\d\-;,，；]+$', cleaned) and len(cleaned.replace('-', '').replace(';', '').replace(',', '').replace('，', '').replace('；', '')) >= 7:
            return cleaned
        
        return None
    
    def _parse_business_hours(self, hours_str: str) -> Optional[str]:
        """解析营业时间字符串"""
        if not hours_str:
            return None
        
        for pattern in self.BUSINESS_HOURS_PATTERNS:
            match = re.search(pattern, hours_str)
            if match:
                return hours_str.strip()
        
        return hours_str.strip() if hours_str.strip() else None
    
    def _parse_price_level(self, cost_info: str) -> Optional[str]:
        """解析价格水平"""
        if not cost_info:
            return None
        
        cost_lower = cost_info.lower()
        
        if '高端' in cost_info or '豪华' in cost_info or '奢华' in cost_info:
            return 'high_end'
        elif '中高' in cost_info or '中等偏上' in cost_info:
            return 'mid_high'
        elif '中等' in cost_info or '适中' in cost_info:
            return 'mid_range'
        elif '经济' in cost_info or '实惠' in cost_info or '便宜' in cost_info:
            return 'budget'
        elif '人均' in cost_info:
            # 尝试提取数字
            numbers = re.findall(r'\d+', cost_info)
            if numbers:
                avg_cost = int(numbers[0])
                if avg_cost >= 200:
                    return 'high_end'
                elif avg_cost >= 100:
                    return 'mid_high'
                elif avg_cost >= 50:
                    return 'mid_range'
                else:
                    return 'budget'
        
        return 'mid_range'  # 默认中等价位
    
    def get_enrichment_stats(self) -> Dict:
        """获取数据补充统计信息"""
        if self.enrichment_stats['total_processed'] > 0:
            success_rate = (self.enrichment_stats['successfully_enriched'] / 
                          self.enrichment_stats['total_processed']) * 100
            self.enrichment_stats['success_rate'] = round(success_rate, 2)
        
        return self.enrichment_stats
    
    def get_mappable_fields(self) -> Dict[str, str]:
        """获取可映射的字段列表"""
        return {
            # 基本信息
            'name': '门店名称',
            'brand_name': '品牌名称',
            'address': '详细地址',
            'longitude': '经度',
            'latitude': '纬度',
            'business_district': '商圈',
            'phone': '联系电话',
            
            # 业务信息
            'store_type': '门店类型',
            'main_business': '主营业务',
            'competition_relation': '竞争关系',
            
            # 营业信息
            'business_hours_weekday': '工作日营业时间',
            'business_hours_weekend': '周末营业时间',
            
            # 评价信息
            'initial_rating': '初始评分',
            'service_quality_estimate': '服务质量评估',
            'estimated_price_level': '价格水平评估',
            
            # 其他信息
            'amap_tags': '高德标签',
            'notes': '备注信息',
            'data_source': '数据来源',
            'amap_poi_id': '高德POI ID',
        }
