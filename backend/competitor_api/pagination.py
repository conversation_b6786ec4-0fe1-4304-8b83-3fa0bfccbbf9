from rest_framework.pagination import PageNumberPagination

class CustomPageNumberPagination(PageNumberPagination):
    page_size = 15
    page_size_query_param = 'page_size'
    max_page_size = 50
    page_size_query_description = "Number of results to return per page. Options: 15, 25, 50"
    
    def get_paginated_response(self, data):
        response = super().get_paginated_response(data)
        response.data['page_size_options'] = [15, 25, 50]
        return response 