from django.shortcuts import render
from django.db.models import Q, <PERSON>, Avg, <PERSON>, <PERSON>, Su<PERSON>, F
from django.db.models.functions import TruncDay, TruncWeek, TruncMonth
from django.utils import timezone
from django.http import HttpResponse
from rest_framework import viewsets, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser, FormParser, JSONParser
from django_filters.rest_framework import DjangoFilterBackend
from math import radians, cos, sin, asin, sqrt
from datetime import datetime, timedelta
import csv
import tempfile
import os
try:
    import openpyxl
    from openpyxl.styles import Font, Alignment, PatternFill
    HAS_OPENPYXL = True
except ImportError:
    HAS_OPENPYXL = False
from .models import (
    CompetitorStore, ServiceOffered, ProductOffered, BrandInfo,
    PricingStrategy, BenchmarkProduct, StoreEnvironment,
    MarketingPromotion, StaffOperations, ComprehensiveRating,
    CompetitorActivity, StoreImage, ActivityImage
)
from .serializers import (
    CompetitorStoreSerializer, CompetitorStoreListSerializer,
    ServiceOfferedSerializer, ProductOfferedSerializer, BrandInfoSerializer,
    PricingStrategySerializer, BenchmarkProductSerializer, StoreEnvironmentSerializer,
    MarketingPromotionSerializer, StaffOperationsSerializer, ComprehensiveRatingSerializer,
    CompetitorActivitySerializer, StoreImageSerializer, ActivityImageSerializer
)
from .amap_data_enricher import AmapDataEnricher

class CompetitorStoreViewSet(viewsets.ModelViewSet):
    """
    API endpoint for competitor stores
    """
    queryset = CompetitorStore.objects.all().order_by('-created_at')
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['store_type', 'competition_relation', 'business_district', 'opening_year']
    search_fields = ['name', 'brand_name', 'address', 'business_district']
    ordering_fields = ['name', 'opening_year', 'created_at', 'updated_at']
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    
    def get_serializer_class(self):
        if self.action == 'list':
            return CompetitorStoreListSerializer
        return CompetitorStoreSerializer
    
    def create(self, request, *args, **kwargs):
        """
        创建新门店时验证是否已存在相同名称和地址的门店
        """
        name = request.data.get('name')
        address = request.data.get('address')
        
        # 检查是否存在具有相同名称和地址的门店
        if name and address:
            existing_store = CompetitorStore.objects.filter(
                name=name, 
                address=address
            ).first()
            
            if existing_store:
                return Response(
                    {
                        'error': '门店已存在',
                        'detail': f'具有相同名称和地址的门店已存在 (ID: {existing_store.id})',
                        'duplicate_store_id': existing_store.id
                    },
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # 如果没有重复，继续创建门店
        return super().create(request, *args, **kwargs)
    
    def get_queryset(self):
        """
        扩展筛选功能：支持面积、人员数量、开业年份范围和品牌等高级筛选
        """
        queryset = super().get_queryset()
        
        # 获取请求参数
        brand_name = self.request.query_params.get('brand_name')
        store_area_min = self.request.query_params.get('store_area_min')
        store_area_max = self.request.query_params.get('store_area_max')
        staff_min = self.request.query_params.get('staff_min')
        staff_max = self.request.query_params.get('staff_max')
        opening_year_min = self.request.query_params.get('opening_year_min')
        opening_year_max = self.request.query_params.get('opening_year_max')
        rating_level = self.request.query_params.get('rating_level')
        
        # 按品牌名筛选
        if brand_name:
            queryset = queryset.filter(brand_name__icontains=brand_name)
        
        # 按门店面积范围筛选
        if store_area_min:
            queryset = queryset.filter(store_area__gte=float(store_area_min))
        if store_area_max:
            queryset = queryset.filter(store_area__lte=float(store_area_max))
        
        # 按人员数量范围筛选 (通过关联表 StaffOperations)
        if staff_min or staff_max:
            if staff_min and not staff_max:
                queryset = queryset.filter(staff__total_staff__gte=int(staff_min))
            elif staff_max and not staff_min:
                queryset = queryset.filter(staff__total_staff__lte=int(staff_max))
            else:
                queryset = queryset.filter(staff__total_staff__gte=int(staff_min), staff__total_staff__lte=int(staff_max))
        
        # 按开业年份范围筛选
        if opening_year_min:
            queryset = queryset.filter(opening_year__gte=int(opening_year_min))
        if opening_year_max:
            queryset = queryset.filter(opening_year__lte=int(opening_year_max))
        
        # 按综合评级筛选
        if rating_level:
            rating_levels = rating_level.split(',')
            queryset = queryset.filter(rating__rating_level__in=rating_levels)
        
        return queryset
    
    @action(detail=True, methods=['post'], url_path='upload-main-image')
    def upload_main_image(self, request, pk=None):
        """上传门店主图片"""
        store = self.get_object()
        if 'image' not in request.FILES:
            return Response({'error': '没有提供图片文件'}, status=status.HTTP_400_BAD_REQUEST)
        
        store.main_image = request.FILES['image']
        store.save()
        
        return Response(CompetitorStoreSerializer(store).data)
    
    @action(detail=False, methods=['get'], url_path='map-search')
    def map_search(self, request):
        """
        地图搜索API：支持按中心点半径搜索和按区域框选搜索
        
        参数:
            center_lng (float): 中心点经度
            center_lat (float): 中心点纬度
            radius_km (float): 搜索半径（公里）
            bounds (string): 框选区域坐标，格式为"lng1,lat1;lng2,lat2"（西南角和东北角）
            其他常规筛选参数
        """
        # 获取基础查询集
        queryset = self.get_queryset()
        
        # 获取地图搜索参数
        center_lng = request.query_params.get('center_lng')
        center_lat = request.query_params.get('center_lat')
        radius_km = request.query_params.get('radius_km')
        bounds = request.query_params.get('bounds')
        
        # 按中心点半径搜索
        if center_lng and center_lat and radius_km:
            try:
                center_lng = float(center_lng)
                center_lat = float(center_lat)
                radius_km = float(radius_km)
                
                # 筛选有经纬度的门店
                queryset = queryset.filter(longitude__isnull=False, latitude__isnull=False)
                
                # 使用Haversine公式计算球面距离
                # 由于计算复杂，我们先用一个粗略的矩形范围过滤，再在Python中精确计算
                # 每纬度约111公里，每经度在中国约85-110公里，取较大值确保不遗漏
                lng_delta = radius_km / 85.0
                lat_delta = radius_km / 111.0
                
                # 粗略过滤
                queryset = queryset.filter(
                    longitude__gte=center_lng - lng_delta,
                    longitude__lte=center_lng + lng_delta,
                    latitude__gte=center_lat - lat_delta,
                    latitude__lte=center_lat + lat_delta
                )
                
                # 精确计算距离并过滤
                filtered_stores = []
                for store in queryset:
                    distance = self.haversine(
                        center_lng, center_lat,
                        store.longitude, store.latitude
                    )
                    if distance <= radius_km:
                        # 添加距离信息到门店对象
                        store.distance_km = round(distance, 2)
                        filtered_stores.append(store)
                
                # 按距离排序
                filtered_stores.sort(key=lambda x: x.distance_km)
                
                # 序列化结果
                page = self.paginate_queryset(filtered_stores)
                if page is not None:
                    serializer = CompetitorStoreListSerializer(page, many=True)
                    return self.get_paginated_response(serializer.data)
                
                serializer = CompetitorStoreListSerializer(filtered_stores, many=True)
                return Response(serializer.data)
                
            except (ValueError, TypeError) as e:
                return Response(
                    {'error': f'参数格式错误: {str(e)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # 按区域框选搜索
        elif bounds:
            try:
                # 解析bounds参数 "lng1,lat1;lng2,lat2"
                bounds_parts = bounds.split(';')
                if len(bounds_parts) != 2:
                    return Response(
                        {'error': 'bounds参数格式错误，应为"lng1,lat1;lng2,lat2"'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                sw_parts = bounds_parts[0].split(',')
                ne_parts = bounds_parts[1].split(',')
                
                if len(sw_parts) != 2 or len(ne_parts) != 2:
                    return Response(
                        {'error': 'bounds参数格式错误，应为"lng1,lat1;lng2,lat2"'},
                        status=status.HTTP_400_BAD_REQUEST
                    )
                
                sw_lng = float(sw_parts[0])
                sw_lat = float(sw_parts[1])
                ne_lng = float(ne_parts[0])
                ne_lat = float(ne_parts[1])
                
                # 筛选有经纬度且在框选区域内的门店
                queryset = queryset.filter(
                    longitude__isnull=False,
                    latitude__isnull=False,
                    longitude__gte=sw_lng,
                    longitude__lte=ne_lng,
                    latitude__gte=sw_lat,
                    latitude__lte=ne_lat
                )
                
                # 序列化结果
                page = self.paginate_queryset(queryset)
                if page is not None:
                    serializer = CompetitorStoreListSerializer(page, many=True)
                    return self.get_paginated_response(serializer.data)
                
                serializer = CompetitorStoreListSerializer(queryset, many=True)
                return Response(serializer.data)
                
            except (ValueError, TypeError, IndexError) as e:
                return Response(
                    {'error': f'参数格式错误: {str(e)}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # 如果没有提供地图搜索参数，返回常规筛选结果
        else:
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = CompetitorStoreListSerializer(page, many=True)
                return self.get_paginated_response(serializer.data)
            
            serializer = CompetitorStoreListSerializer(queryset, many=True)
            return Response(serializer.data)
    
    @action(detail=False, methods=['post'], url_path='map-import')
    def map_import(self, request):
        """
        从地图搜索结果导入门店数据
        
        请求体格式:
        {
            "stores": [
                {
                    "name": "门店名称",
                    "address": "详细地址",
                    "longitude": 经度,
                    "latitude": 纬度,
                    "phone": "电话号码",  # 可选，多个电话号码用分号分隔
                    "brand_name": "品牌名"  # 可选，如不提供将自动从店铺名提取
                },
                # ... 更多门店
            ]
        }
        
        返回:
        {
            "code": 0,           # 响应码：0表示成功
            "total": 总数量,
            "success_count": 成功数量,
            "duplicate_count": 重复数量,
            "error_count": 错误数量,
            "message": "处理完成",
            "success": [已成功导入的门店信息],
            "duplicates": [已存在的门店信息],
            "errors": [导入失败的门店信息及错误原因]
        }
        """
        stores_data = request.data.get('stores', [])
        if not stores_data:
            return Response({
                "code": 1,
                "message": "未提供门店数据",
                "total": 0,
                "success_count": 0,
                "duplicate_count": 0,
                "error_count": 0,
                "success": [],
                "duplicates": [],
                "errors": []
            }, status=status.HTTP_400_BAD_REQUEST)
        
        results = {
            "success": [],
            "duplicates": [],
            "errors": []
        }
        
        for store_data in stores_data:
            name = store_data.get('name')
            address = store_data.get('address')
            
            if not name or not address:
                results["errors"].append({
                    "data": store_data,
                    "error": "门店名称和地址不能为空"
                })
                continue
            
            # 检查门店是否已存在
            existing_store = CompetitorStore.objects.filter(
                name=name, 
                address=address
            ).first()
            
            if existing_store:
                results["duplicates"].append({
                    "data": store_data,
                    "duplicate_id": existing_store.id,
                    "name": existing_store.name,
                    "address": existing_store.address
                })
                continue
            
            # 准备要创建的门店数据
            store_obj = {
                "name": name,
                "address": address,
                "longitude": store_data.get('longitude'),
                "latitude": store_data.get('latitude'),
            }
            
            # 自动提取品牌名
            brand_name = store_data.get('brand_name')
            if not brand_name:
                # 从店铺名称智能提取品牌名
                brand_name = self.extract_brand_name(name)
            
            store_obj["brand_name"] = brand_name
            
            # 处理电话号码字段
            phone = store_data.get('phone')
            # 确保电话号码字段始终存在，可以为空字符串或None
            # 处理不同类型的phone值
            if phone is not None:
                if isinstance(phone, list):
                    # 如果是列表，尝试将其转换为分号分隔的字符串
                    phone = ';'.join(filter(None, [str(p).strip() if p else '' for p in phone]))
                    store_obj["phone"] = phone if phone else None
                elif isinstance(phone, str):
                    # 如果是字符串，去除前后空格
                    store_obj["phone"] = phone.strip() if phone.strip() else None
                else:
                    # 其他类型转为字符串
                    try:
                        phone_str = str(phone).strip()
                        store_obj["phone"] = phone_str if phone_str else None
                    except:
                        store_obj["phone"] = None
            else:
                store_obj["phone"] = None
            
            # 设置默认值
            store_obj["store_type"] = store_data.get('store_type', "single_store")  # 默认为单体店
            store_obj["competition_relation"] = store_data.get('competition_relation', "direct_competitor")  # 默认为直接竞争对手
            
            # 使用序列化器创建门店
            serializer = CompetitorStoreSerializer(data=store_obj)
            try:
                if serializer.is_valid(raise_exception=True):
                    new_store = serializer.save()
                    results["success"].append({
                        "id": new_store.id,
                        "name": new_store.name,
                        "address": new_store.address,
                        "brand_name": new_store.brand_name
                    })
            except Exception as e:
                results["errors"].append({
                    "data": store_data,
                    "error": str(e)
                })
        
        # 添加统计信息
        total = len(stores_data)
        success_count = len(results["success"])
        duplicate_count = len(results["duplicates"])
        error_count = len(results["errors"])
        
        # 构建最终响应
        response_data = {
            "code": 0,  # 0表示成功
            "message": "处理完成",
            "total": total,
            "success_count": success_count,
            "duplicate_count": duplicate_count,
            "error_count": error_count,
            **results  # 展开原始结果
        }
        
        return Response(response_data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'], url_path='enrich-from-amap')
    def enrich_from_amap(self, request):
        """
        从高德POI数据自动补充门店信息

        请求体格式:
        {
            "poi_data": {
                "id": "B000A7BD6C",
                "name": "宠物店名称",
                "address": "详细地址",
                "location": "116.123,39.456",
                "tel": "010-12345678",
                "typecode": "061211",
                "type": "宠物服务",
                "distance": 1500,
                "biz_ext": {
                    "open_time": "09:00-21:00",
                    "rating": "4.5",
                    "cost": "人均消费100元",
                    "tag": "宠物美容,洗澡"
                }
            },
            "create_if_not_exists": true  // 如果门店不存在是否创建新门店
        }

        返回:
        {
            "success": true,
            "action": "created|updated|found",
            "store_id": 门店ID,
            "enriched_fields": ["字段1", "字段2"],
            "enrichment_stats": {...}
        }
        """
        poi_data = request.data.get('poi_data', {})
        create_if_not_exists = request.data.get('create_if_not_exists', True)

        if not poi_data:
            return Response({
                "success": false,
                "error": "未提供POI数据"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 初始化数据增强器
        enricher = AmapDataEnricher()

        # 从POI数据中提取门店信息
        enriched_data = enricher.enrich_store_data(poi_data)

        if not enriched_data:
            return Response({
                "success": false,
                "error": "POI数据解析失败"
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查门店是否已存在
        name = enriched_data.get('name')
        address = enriched_data.get('address')

        existing_store = None
        if name and address:
            existing_store = CompetitorStore.objects.filter(
                name=name,
                address=address
            ).first()

        action = "found"
        enriched_fields = []

        if existing_store:
            # 更新现有门店的空字段
            updated_fields = self._update_empty_fields(existing_store, enriched_data)
            if updated_fields:
                existing_store.save()
                action = "updated"
                enriched_fields = updated_fields

            store_id = existing_store.id

        elif create_if_not_exists:
            # 创建新门店
            serializer = CompetitorStoreSerializer(data=enriched_data)
            try:
                if serializer.is_valid(raise_exception=True):
                    new_store = serializer.save()
                    action = "created"
                    enriched_fields = list(enriched_data.keys())
                    store_id = new_store.id
                else:
                    return Response({
                        "success": false,
                        "error": "门店数据验证失败",
                        "validation_errors": serializer.errors
                    }, status=status.HTTP_400_BAD_REQUEST)
            except Exception as e:
                return Response({
                    "success": false,
                    "error": f"创建门店失败: {str(e)}"
                }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        else:
            return Response({
                "success": false,
                "error": "门店不存在且未设置自动创建"
            }, status=status.HTTP_404_NOT_FOUND)

        return Response({
            "success": True,
            "action": action,
            "store_id": store_id,
            "enriched_fields": enriched_fields,
            "enrichment_stats": enricher.get_enrichment_stats(),
            "mappable_fields": enricher.get_mappable_fields()
        })

    @action(detail=False, methods=['post'], url_path='batch-enrich-from-amap')
    def batch_enrich_from_amap(self, request):
        """
        批量从高德POI数据补充门店信息

        请求体格式:
        {
            "poi_list": [
                {POI数据1},
                {POI数据2},
                ...
            ],
            "create_if_not_exists": true,
            "update_existing": true  // 是否更新已存在门店的空字段
        }
        """
        poi_list = request.data.get('poi_list', [])
        create_if_not_exists = request.data.get('create_if_not_exists', True)
        update_existing = request.data.get('update_existing', True)

        if not poi_list:
            return Response({
                "success": false,
                "error": "未提供POI数据列表"
            }, status=status.HTTP_400_BAD_REQUEST)

        enricher = AmapDataEnricher()
        results = {
            "created": [],
            "updated": [],
            "found": [],
            "errors": []
        }

        for poi_data in poi_list:
            try:
                # 处理单个POI
                enriched_data = enricher.enrich_store_data(poi_data)

                if not enriched_data:
                    results["errors"].append({
                        "poi_id": poi_data.get('id'),
                        "error": "POI数据解析失败"
                    })
                    continue

                name = enriched_data.get('name')
                address = enriched_data.get('address')

                existing_store = None
                if name and address:
                    existing_store = CompetitorStore.objects.filter(
                        name=name,
                        address=address
                    ).first()

                if existing_store:
                    if update_existing:
                        updated_fields = self._update_empty_fields(existing_store, enriched_data)
                        if updated_fields:
                            existing_store.save()
                            results["updated"].append({
                                "store_id": existing_store.id,
                                "name": existing_store.name,
                                "updated_fields": updated_fields
                            })
                        else:
                            results["found"].append({
                                "store_id": existing_store.id,
                                "name": existing_store.name
                            })
                    else:
                        results["found"].append({
                            "store_id": existing_store.id,
                            "name": existing_store.name
                        })

                elif create_if_not_exists:
                    serializer = CompetitorStoreSerializer(data=enriched_data)
                    if serializer.is_valid():
                        new_store = serializer.save()
                        results["created"].append({
                            "store_id": new_store.id,
                            "name": new_store.name,
                            "enriched_fields": list(enriched_data.keys())
                        })
                    else:
                        results["errors"].append({
                            "poi_id": poi_data.get('id'),
                            "error": "门店数据验证失败",
                            "validation_errors": serializer.errors
                        })

            except Exception as e:
                results["errors"].append({
                    "poi_id": poi_data.get('id'),
                    "error": str(e)
                })

        # 统计信息
        total_processed = len(poi_list)
        total_created = len(results["created"])
        total_updated = len(results["updated"])
        total_found = len(results["found"])
        total_errors = len(results["errors"])

        return Response({
            "success": True,
            "total_processed": total_processed,
            "created_count": total_created,
            "updated_count": total_updated,
            "found_count": total_found,
            "error_count": total_errors,
            "results": results,
            "enrichment_stats": enricher.get_enrichment_stats()
        })

    def _update_empty_fields(self, store, enriched_data):
        """更新门店的空字段"""
        updated_fields = []

        # 定义可以自动更新的字段
        updatable_fields = [
            'brand_name', 'phone', 'business_district',
            'business_hours_weekday', 'business_hours_weekend',
            'longitude', 'latitude', 'notes'
        ]

        for field in updatable_fields:
            if field in enriched_data and enriched_data[field]:
                current_value = getattr(store, field, None)
                # 只更新空字段
                if not current_value:
                    setattr(store, field, enriched_data[field])
                    updated_fields.append(field)

        return updated_fields

    def extract_brand_name(self, store_name):
        """
        从店铺名称智能提取品牌名
        例如：
        - "嫒宠宠物店" -> "嫒宠"
        - "星辰宠艺宠物美容培训(蓝光COCO金沙3期耍街)" -> "星辰宠艺"
        """
        if not store_name:
            return ""
        
        # 常见的店铺后缀关键词
        suffixes = [
            "宠物店", "宠物医院", "宠物美容", "宠物诊所", "宠物馆", "宠物会所", 
            "宠物生活馆", "宠物用品", "动物医院", "宠物SPA", "宠物spa", 
            "宠物用品店", "宠物酒店", "宠物"
        ]
        
        # 处理带括号的情况，取括号前的部分
        name_parts = store_name.split("(") if "(" in store_name else store_name.split("（")
        base_name = name_parts[0].strip()
        
        # 去除后缀获取品牌名
        for suffix in suffixes:
            if suffix in base_name:
                # 特殊处理：如果在开头就包含"宠物"，则不作为后缀处理
                if suffix == "宠物" and base_name.startswith("宠物"):
                    continue
                brand_name = base_name.split(suffix)[0].strip()
                return brand_name
        
        # 如果没有明确的后缀，返回处理过的基础名称
        # 如果名称中包含空格，取第一部分
        if " " in base_name:
            base_name = base_name.split(" ")[0]
            
        return base_name
    
    def haversine(self, lon1, lat1, lon2, lat2):
        """
        计算两点间的球面距离（单位：公里）
        使用Haversine公式
        """
        # 将十进制度数转化为弧度
        lon1, lat1, lon2, lat2 = map(radians, [lon1, lat1, lon2, lat2])
        
        # Haversine公式
        dlon = lon2 - lon1 
        dlat = lat2 - lat1 
        a = sin(dlat/2)**2 + cos(lat1) * cos(lat2) * sin(dlon/2)**2
        c = 2 * asin(sqrt(a)) 
        r = 6371  # 地球平均半径，单位为公里
        return c * r

    @action(detail=False, methods=['get'], url_path='statistics')
    def get_statistics(self, request):
        """
        获取门店统计数据
        支持以下统计：
        - 总门店数量
        - 本月新增门店数量
        - 最近7天更新的门店数量
        
        支持按区域、时间段过滤：
        - business_district: 商圈/区域名称
        - store_type: 门店类型
        - competition_relation: 竞争关系
        - start_date: 开始日期 (YYYY-MM-DD)
        - end_date: 结束日期 (YYYY-MM-DD)
        """
        # 获取今天的日期
        today = timezone.now().date()
        
        # 计算本月第一天
        first_day_of_month = today.replace(day=1)
        
        # 计算7天前的日期
        seven_days_ago = today - timedelta(days=7)
        
        # 获取过滤参数
        business_district = request.query_params.get('business_district')
        store_type = request.query_params.get('store_type')
        competition_relation = request.query_params.get('competition_relation')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        # 初始化查询集
        queryset = CompetitorStore.objects.all()
        
        # 应用过滤条件
        if business_district:
            queryset = queryset.filter(business_district=business_district)
        
        if store_type:
            queryset = queryset.filter(store_type=store_type)
        
        if competition_relation:
            queryset = queryset.filter(competition_relation=competition_relation)
        
        # 解析日期过滤
        try:
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=start_date)
            
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=end_date)
        except ValueError:
            return Response(
                {'error': '日期格式错误，请使用YYYY-MM-DD格式'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取总门店数量（应用过滤条件）
        total_stores = queryset.count()
        
        # 获取本月新增门店数量（应用过滤条件）
        new_stores_this_month = queryset.filter(
            created_at__date__gte=first_day_of_month
        ).count()
        
        # 获取最近7天更新的门店数量（应用过滤条件）
        updated_stores_last_week = queryset.filter(
            updated_at__date__gte=seven_days_ago
        ).count()
        
        # 按门店类型统计
        store_type_stats = queryset.values('store_type').annotate(
            count=Count('id')
        ).order_by('store_type')
        
        # 按竞争关系统计
        competition_relation_stats = queryset.values('competition_relation').annotate(
            count=Count('id')
        ).order_by('competition_relation')
        
        # 按商圈区域统计（过滤掉空值）
        business_district_stats = queryset.exclude(
            business_district__isnull=True
        ).exclude(
            business_district__exact=''
        ).values('business_district').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 取前10个最多的商圈
        
        # 按评级统计
        rating_stats = queryset.filter(
            rating__isnull=False
        ).values('rating__rating_level').annotate(
            count=Count('id')
        ).order_by('rating__rating_level')
        
        # 按月统计新增门店
        # 获取最近12个月的分布
        months_stats = []
        for i in range(12):
            month_date = (today.replace(day=1) - timedelta(days=1)).replace(day=1)
            month_date = month_date.replace(month=((today.month - i - 1) % 12) + 1)
            if month_date.month > today.month:
                month_date = month_date.replace(year=today.year - 1)
            
            month_count = queryset.filter(
                created_at__year=month_date.year,
                created_at__month=month_date.month
            ).count()
            
            months_stats.append({
                'year': month_date.year,
                'month': month_date.month,
                'count': month_count
            })
        
        # 构建响应数据
        response_data = {
            "total_stores": total_stores,
            "new_stores_this_month": new_stores_this_month,
            "updated_stores_last_week": updated_stores_last_week,
            "store_type_distribution": store_type_stats,
            "competition_relation_distribution": competition_relation_stats,
            "business_district_distribution": business_district_stats,
            "rating_distribution": rating_stats,
            "monthly_new_stores": months_stats,
            "filters_applied": {
                "business_district": business_district,
                "store_type": store_type,
                "competition_relation": competition_relation,
                "start_date": start_date_str,
                "end_date": end_date_str
            },
            "date_info": {
                "today": today.isoformat(),
                "first_day_of_month": first_day_of_month.isoformat(),
                "seven_days_ago": seven_days_ago.isoformat()
            }
        }
        
        return Response(response_data)

    @action(detail=False, methods=['get'], url_path='export')
    def export_data(self, request):
        """
        导出门店数据为CSV或Excel文件
        支持按区域、时间段过滤：
        - business_district: 商圈/区域名称
        - store_type: 门店类型
        - competition_relation: 竞争关系
        - start_date: 开始日期 (YYYY-MM-DD)
        - end_date: 结束日期 (YYYY-MM-DD)
        - format: 导出格式 (csv 或 excel，默认excel)
        """
        # 获取过滤参数
        business_district = request.query_params.get('business_district')
        store_type = request.query_params.get('store_type')
        competition_relation = request.query_params.get('competition_relation')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        export_format = request.query_params.get('format', 'excel').lower()
        
        # 初始化查询集
        queryset = CompetitorStore.objects.all()
        
        # 应用过滤条件
        if business_district:
            queryset = queryset.filter(business_district=business_district)
        
        if store_type:
            queryset = queryset.filter(store_type=store_type)
        
        if competition_relation:
            queryset = queryset.filter(competition_relation=competition_relation)
        
        # 解析日期过滤
        try:
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=start_date)
            
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=end_date)
        except ValueError:
            return Response(
                {'error': '日期格式错误，请使用YYYY-MM-DD格式'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取今天的日期作为文件名的一部分
        today = timezone.now().strftime('%Y%m%d')
        
        # 定义表头字段
        headers = [
            'ID', '门店名称', '品牌名', '门店类型', '竞争关系', '详细地址', 
            '经度', '纬度', '所属商圈/区域', '交通便利性', '可见性与易达性',
            '周边社区类型', '联系电话', '营业时间(工作日)', '营业时间(周末/节假日)',
            '开业年份', '门店面积(m²)', '备注', '创建时间', '更新时间'
        ]
        
        # 转换为易读的字段名
        store_type_dict = dict(CompetitorStore.STORE_TYPE_CHOICES)
        competition_relation_dict = dict(CompetitorStore.COMPETITION_RELATION_CHOICES)
        visibility_dict = dict(CompetitorStore.VISIBILITY_CHOICES)
        
        if export_format == 'csv':
            # CSV导出
            response = HttpResponse(content_type='text/csv')
            response['Content-Disposition'] = f'attachment; filename="stores_export_{today}.csv"'
            
            # 创建CSV写入器
            writer = csv.writer(response)
            writer.writerow(headers)
            
            # 写入数据行
            for store in queryset:
                writer.writerow([
                    store.id,
                    store.name,
                    store.brand_name or '',
                    store_type_dict.get(store.store_type, ''),
                    competition_relation_dict.get(store.competition_relation, ''),
                    store.address,
                    store.longitude or '',
                    store.latitude or '',
                    store.business_district or '',
                    store.transportation_convenience or '',
                    visibility_dict.get(store.visibility, '') if store.visibility else '',
                    store.surrounding_community_type or '',
                    store.phone or '',
                    store.business_hours_weekday or '',
                    store.business_hours_weekend or '',
                    store.opening_year or '',
                    store.store_area or '',
                    store.notes or '',
                    store.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                    store.updated_at.strftime('%Y-%m-%d %H:%M:%S')
                ])
            
            return response
            
        elif export_format == 'excel':
            # 检查是否安装了openpyxl
            if not HAS_OPENPYXL:
                return Response(
                    {'error': '服务器未安装openpyxl库，无法导出Excel格式，请选择CSV格式或联系管理员'},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            
            # 创建Excel工作簿
            wb = openpyxl.Workbook()
            ws = wb.active
            ws.title = "门店数据"
            
            # 设置表头样式
            header_font = Font(bold=True, color="FFFFFF")
            header_fill = PatternFill(start_color="4472C4", end_color="4472C4", fill_type="solid")
            header_alignment = Alignment(horizontal="center", vertical="center")
            
            # 写入表头
            for col_idx, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_idx, value=header)
                cell.font = header_font
                cell.fill = header_fill
                cell.alignment = header_alignment
            
            # 写入数据
            for row_idx, store in enumerate(queryset, 2):
                ws.cell(row=row_idx, column=1, value=store.id)
                ws.cell(row=row_idx, column=2, value=store.name)
                ws.cell(row=row_idx, column=3, value=store.brand_name or '')
                ws.cell(row=row_idx, column=4, value=store_type_dict.get(store.store_type, ''))
                ws.cell(row=row_idx, column=5, value=competition_relation_dict.get(store.competition_relation, ''))
                ws.cell(row=row_idx, column=6, value=store.address)
                ws.cell(row=row_idx, column=7, value=store.longitude)
                ws.cell(row=row_idx, column=8, value=store.latitude)
                ws.cell(row=row_idx, column=9, value=store.business_district or '')
                ws.cell(row=row_idx, column=10, value=store.transportation_convenience or '')
                ws.cell(row=row_idx, column=11, value=visibility_dict.get(store.visibility, '') if store.visibility else '')
                ws.cell(row=row_idx, column=12, value=store.surrounding_community_type or '')
                ws.cell(row=row_idx, column=13, value=store.phone or '')
                ws.cell(row=row_idx, column=14, value=store.business_hours_weekday or '')
                ws.cell(row=row_idx, column=15, value=store.business_hours_weekend or '')
                ws.cell(row=row_idx, column=16, value=store.opening_year)
                ws.cell(row=row_idx, column=17, value=store.store_area)
                ws.cell(row=row_idx, column=18, value=store.notes or '')
                ws.cell(row=row_idx, column=19, value=store.created_at.strftime('%Y-%m-%d %H:%M:%S'))
                ws.cell(row=row_idx, column=20, value=store.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            
            # 调整列宽
            for col in range(1, len(headers) + 1):
                ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
            
            # 自动筛选
            ws.auto_filter.ref = f"A1:{openpyxl.utils.get_column_letter(len(headers))}{queryset.count() + 1}"
            
            # 冻结首行
            ws.freeze_panes = "A2"
            
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as tmp:
                wb.save(tmp.name)
                tmp_path = tmp.name
            
            with open(tmp_path, 'rb') as f:
                response = HttpResponse(
                    f.read(),
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename="stores_export_{today}.xlsx"'
            
            # 删除临时文件
            os.unlink(tmp_path)
            
            return response
        
        else:
            return Response(
                {'error': '不支持的导出格式，请选择 csv 或 excel'},
                status=status.HTTP_400_BAD_REQUEST
            )

class StoreImageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for store images
    """
    queryset = StoreImage.objects.all().order_by('order', 'uploaded_at')
    serializer_class = StoreImageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['store']
    parser_classes = [MultiPartParser, FormParser, JSONParser]

class ServiceOfferedViewSet(viewsets.ModelViewSet):
    queryset = ServiceOffered.objects.all()
    serializer_class = ServiceOfferedSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'service_category', 'has_cat_area']
    search_fields = ['service_name', 'service_description', 'product_brand']

class ProductOfferedViewSet(viewsets.ModelViewSet):
    queryset = ProductOffered.objects.all()
    serializer_class = ProductOfferedSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'has_special_products']
    search_fields = ['main_product_categories', 'special_products_description']

class BrandInfoViewSet(viewsets.ModelViewSet):
    queryset = BrandInfo.objects.all()
    serializer_class = BrandInfoSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['product', 'category', 'positioning']
    search_fields = ['brand_name', 'notes']

class PricingStrategyViewSet(viewsets.ModelViewSet):
    queryset = PricingStrategy.objects.all()
    serializer_class = PricingStrategySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'price_level', 'has_membership', 'promotion_frequency']
    search_fields = ['membership_types', 'membership_benefits', 'promotion_types']

class BenchmarkProductViewSet(viewsets.ModelViewSet):
    queryset = BenchmarkProduct.objects.all()
    serializer_class = BenchmarkProductSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['pricing']
    search_fields = ['product_name']

class StoreEnvironmentViewSet(viewsets.ModelViewSet):
    queryset = StoreEnvironment.objects.all()
    serializer_class = StoreEnvironmentSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'cleanliness', 'smell', 'lighting', 'ventilation']
    search_fields = ['decoration_style', 'beauty_equipment', 'retail_display', 'customer_facilities']

class MarketingPromotionViewSet(viewsets.ModelViewSet):
    queryset = MarketingPromotion.objects.all()
    serializer_class = MarketingPromotionSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'online_activity']
    search_fields = ['wechat_account', 'wechat_miniapp', 'positive_keywords', 'negative_keywords', 'brand_perception']

class StaffOperationsViewSet(viewsets.ModelViewSet):
    queryset = StaffOperations.objects.all()
    serializer_class = StaffOperationsSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'service_attitude', 'appointment_fullness']
    search_fields = ['customer_flow']

class ComprehensiveRatingViewSet(viewsets.ModelViewSet):
    queryset = ComprehensiveRating.objects.all()
    serializer_class = ComprehensiveRatingSerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['store', 'rating_level']
    search_fields = ['overall_evaluation']

class CompetitorActivityViewSet(viewsets.ModelViewSet):
    queryset = CompetitorActivity.objects.all().order_by('-start_date', '-created_at')
    serializer_class = CompetitorActivitySerializer
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['store', 'activity_type', 'status', 'impact_level', 'start_date', 'end_date']
    search_fields = ['title', 'description', 'marketing_channels', 'target_customers']
    ordering_fields = ['start_date', 'end_date', 'created_at', 'impact_level']
    parser_classes = [MultiPartParser, FormParser, JSONParser]
    
    @action(detail=True, methods=['post'], url_path='upload-main-image')
    def upload_main_image(self, request, pk=None):
        """上传活动主图片"""
        activity = self.get_object()
        if 'image' not in request.FILES:
            return Response({'error': '没有提供图片文件'}, status=status.HTTP_400_BAD_REQUEST)
        
        activity.main_image = request.FILES['image']
        activity.save()
        
        return Response(CompetitorActivitySerializer(activity).data)
    
    @action(detail=False, methods=['get'], url_path='statistics')
    def get_activity_statistics(self, request):
        """
        获取活动统计数据
        支持以下统计：
        - 当前进行中的活动数量
        - 即将开始的活动数量
        - 已结束的活动数量
        - 按活动类型的分布
        - 按影响级别的分布
        - 活动时间段分布
        
        支持按以下条件过滤：
        - store: 门店ID
        - activity_type: 活动类型
        - impact_level: 影响级别
        - start_date: 开始日期 (YYYY-MM-DD)
        - end_date: 结束日期 (YYYY-MM-DD)
        """
        # 获取今天的日期
        today = timezone.now().date()
        
        # 获取过滤参数
        store_id = request.query_params.get('store')
        activity_type = request.query_params.get('activity_type')
        impact_level = request.query_params.get('impact_level')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        # 初始化查询集
        queryset = CompetitorActivity.objects.all()
        
        # 应用过滤条件
        if store_id:
            queryset = queryset.filter(store_id=store_id)
        
        if activity_type:
            queryset = queryset.filter(activity_type=activity_type)
        
        if impact_level:
            queryset = queryset.filter(impact_level=impact_level)
        
        # 解析日期过滤
        try:
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(start_date__gte=start_date)
            
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
                queryset = queryset.filter(end_date__lte=end_date)
        except ValueError:
            return Response(
                {'error': '日期格式错误，请使用YYYY-MM-DD格式'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 获取活动状态统计
        active_activities = queryset.filter(status='active').count()
        upcoming_activities = queryset.filter(status='upcoming').count()
        ended_activities = queryset.filter(status='ended').count()
        recurring_activities = queryset.filter(status='recurring').count()
        
        # 按活动类型统计
        activity_type_stats = queryset.values('activity_type').annotate(
            count=Count('id')
        ).order_by('activity_type')
        
        # 按影响级别统计
        impact_level_stats = queryset.values('impact_level').annotate(
            count=Count('id')
        ).order_by('impact_level')
        
        # 按门店统计
        store_activity_stats = queryset.values('store__name').annotate(
            count=Count('id')
        ).order_by('-count')[:10]  # 取前10个最活跃的门店
        
        # 按月统计活动分布
        # 获取最近6个月的分布
        months_stats = []
        for i in range(6):
            month_date = (today.replace(day=1) - timedelta(days=1)).replace(day=1)
            month_date = month_date.replace(month=((today.month - i - 1) % 12) + 1)
            if month_date.month > today.month:
                month_date = month_date.replace(year=today.year - 1)
            
            month_count = queryset.filter(
                start_date__year=month_date.year,
                start_date__month=month_date.month
            ).count()
            
            months_stats.append({
                'year': month_date.year,
                'month': month_date.month,
                'count': month_count
            })
        
        # 构建响应数据
        response_data = {
            "activity_status": {
                "active": active_activities,
                "upcoming": upcoming_activities,
                "ended": ended_activities,
                "recurring": recurring_activities,
                "total": queryset.count()
            },
            "activity_type_distribution": activity_type_stats,
            "impact_level_distribution": impact_level_stats,
            "store_activity_distribution": store_activity_stats,
            "monthly_activity_distribution": months_stats,
            "filters_applied": {
                "store_id": store_id,
                "activity_type": activity_type,
                "impact_level": impact_level,
                "start_date": start_date_str,
                "end_date": end_date_str
            },
            "date_info": {
                "today": today.isoformat()
            }
        }
        
        return Response(response_data)

class ActivityImageViewSet(viewsets.ModelViewSet):
    """
    API endpoint for activity images
    """
    queryset = ActivityImage.objects.all().order_by('order', 'uploaded_at')
    serializer_class = ActivityImageSerializer
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['activity']
    parser_classes = [MultiPartParser, FormParser, JSONParser]

# 添加一个新的视图类，专门用于数据分析
class AnalyticsViewSet(viewsets.ViewSet):
    """数据分析视图集"""
    
    @action(detail=False, methods=['get'], url_path='trends')
    def get_trends(self, request):
        """
        获取数据趋势分析
        
        支持以下参数：
        - metric: 指标名称，支持 "new_stores"、"activities"、"ratings"
        - period: 周期，支持 "day"、"week"、"month"（默认为 "month"）
        - start_date: 开始日期，格式：YYYY-MM-DD（可选）
        - end_date: 结束日期，格式：YYYY-MM-DD（可选）
        - store_type: 门店类型（可选）
        - business_district: 商圈/区域名称（可选）
        """
        # 获取参数
        metric = request.query_params.get('metric')
        period = request.query_params.get('period', 'month').lower()
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        store_type = request.query_params.get('store_type')
        business_district = request.query_params.get('business_district')
        
        # 验证指标名称
        valid_metrics = ['new_stores', 'activities', 'ratings']
        if not metric or metric not in valid_metrics:
            return Response(
                {'error': f'无效的指标名称，请选择 {", ".join(valid_metrics)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 验证周期
        valid_periods = ['day', 'week', 'month']
        if period not in valid_periods:
            return Response(
                {'error': f'无效的周期，请选择 {", ".join(valid_periods)}'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 解析日期
        today = timezone.now().date()
        try:
            if start_date_str:
                start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            else:
                # 默认为6个月前
                start_date = (today.replace(day=1) - timedelta(days=180)).replace(day=1)
            
            if end_date_str:
                end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
            else:
                end_date = today
        except ValueError:
            return Response(
                {'error': '日期格式错误，请使用YYYY-MM-DD格式'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # 选择适当的日期截断函数
        if period == 'day':
            truncate_func = TruncDay
            date_format = '%Y-%m-%d'
        elif period == 'week':
            truncate_func = TruncWeek
            date_format = '%Y-%W'  # 年-周数
        else:  # month
            truncate_func = TruncMonth
            date_format = '%Y-%m'
        
        # 根据指标类型选择不同的查询逻辑
        if metric == 'new_stores':
            # 查询门店创建趋势
            queryset = CompetitorStore.objects.filter(
                created_at__date__gte=start_date,
                created_at__date__lte=end_date
            )
            
            # 应用额外的过滤条件
            if store_type:
                queryset = queryset.filter(store_type=store_type)
            
            if business_district:
                queryset = queryset.filter(business_district=business_district)
            
            # 按周期分组统计
            trends_data = queryset.annotate(
                period=truncate_func('created_at')
            ).values('period').annotate(
                count=Count('id')
            ).order_by('period')
            
            # 格式化结果
            trends = [
                {
                    'period_label': item['period'].strftime(date_format),
                    'value': item['count']
                }
                for item in trends_data
            ]
            
            # 计算汇总统计
            total = sum(item['count'] for item in trends_data)
            avg_per_period = round(total / len(trends) if trends else 0, 2)
            
            if trends:
                max_item = max(trends, key=lambda x: x['value'])
                min_item = min(trends, key=lambda x: x['value'])
                max_period = max_item['period_label']
                max_value = max_item['value']
                min_period = min_item['period_label']
                min_value = min_item['value']
            else:
                max_period = None
                max_value = 0
                min_period = None
                min_value = 0
            
        elif metric == 'activities':
            # 查询活动趋势
            queryset = CompetitorActivity.objects.filter(
                start_date__gte=start_date,
                start_date__lte=end_date
            )
            
            # 应用额外的过滤条件
            if store_type or business_district:
                # 需要连接门店表
                queryset = queryset.select_related('store')
                
                if store_type:
                    queryset = queryset.filter(store__store_type=store_type)
                
                if business_district:
                    queryset = queryset.filter(store__business_district=business_district)
            
            # 按周期分组统计
            trends_data = queryset.annotate(
                period=truncate_func('start_date')
            ).values('period').annotate(
                count=Count('id')
            ).order_by('period')
            
            # 格式化结果
            trends = [
                {
                    'period_label': item['period'].strftime(date_format),
                    'value': item['count']
                }
                for item in trends_data
            ]
            
            # 计算汇总统计
            total = sum(item['count'] for item in trends_data)
            avg_per_period = round(total / len(trends) if trends else 0, 2)
            
            if trends:
                max_item = max(trends, key=lambda x: x['value'])
                min_item = min(trends, key=lambda x: x['value'])
                max_period = max_item['period_label']
                max_value = max_item['value']
                min_period = min_item['period_label']
                min_value = min_item['value']
            else:
                max_period = None
                max_value = 0
                min_period = None
                min_value = 0
            
        elif metric == 'ratings':
            # 查询评级趋势（基于更新时间）
            queryset = ComprehensiveRating.objects.filter(
                store__updated_at__date__gte=start_date,
                store__updated_at__date__lte=end_date
            )
            
            # 应用额外的过滤条件
            if store_type:
                queryset = queryset.filter(store__store_type=store_type)
            
            if business_district:
                queryset = queryset.filter(store__business_district=business_district)
            
            # 按周期分组统计平均评分
            trends_data = queryset.annotate(
                period=truncate_func('store__updated_at')
            ).values('period').annotate(
                avg_score=Avg('total_score')
            ).order_by('period')
            
            # 格式化结果
            trends = [
                {
                    'period_label': item['period'].strftime(date_format),
                    'value': round(item['avg_score'], 2) if item['avg_score'] is not None else 0
                }
                for item in trends_data
            ]
            
            # 计算汇总统计
            scores = [item['value'] for item in trends]
            total = sum(scores)
            avg_per_period = round(total / len(trends) if trends else 0, 2)
            
            if trends:
                max_item = max(trends, key=lambda x: x['value'])
                min_item = min(trends, key=lambda x: x['value'])
                max_period = max_item['period_label']
                max_value = max_item['value']
                min_period = min_item['period_label']
                min_value = min_item['value']
            else:
                max_period = None
                max_value = 0
                min_period = None
                min_value = 0
        
        # 构建响应数据
        response_data = {
            "metric": metric,
            "period": period,
            "trends": trends,
            "filters_applied": {
                "start_date": start_date.isoformat(),
                "end_date": end_date.isoformat(),
                "store_type": store_type,
                "business_district": business_district
            },
            "summary": {
                "total": total,
                "avg_per_period": avg_per_period,
                "max_period": max_period,
                "max_value": max_value,
                "min_period": min_period,
                "min_value": min_value
            }
        }
        
        return Response(response_data)
