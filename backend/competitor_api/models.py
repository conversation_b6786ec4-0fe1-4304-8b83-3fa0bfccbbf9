from django.db import models

# Create your models here.

class CompetitorStore(models.Model):
    """基本信息 (Basic Information)"""
    STORE_TYPE_CHOICES = [
        ('direct_chain', '直营连锁'),
        ('franchise_chain', '加盟连锁'),
        ('single_store', '单体店'),
        ('pet_hospital', '宠物医院附带'),
        ('retail_only', '纯零售店'),
    ]
    
    COMPETITION_RELATION_CHOICES = [
        ('direct_competitor', '直接竞争对手'),
        ('indirect_hospital', '间接竞争对手-宠物医院'),
        ('indirect_retail', '间接竞争对手-纯零售'),
    ]
    
    VISIBILITY_CHOICES = [
        ('excellent', '极佳'),
        ('good', '良好'),
        ('average', '一般'),
        ('poor', '较差'),
    ]
    
    MAIN_BUSINESS_CHOICES = [
        ('pet_grooming', '宠物洗护'),
        ('pet_beauty', '宠物美容'),
        ('pet_sale', '活体销售'),
        ('exotic_pet_sale', '异宠销售'),
        ('pet_supplies', '宠物用品'),
        ('pet_fresh_food', '宠物鲜食'),
        ('pet_training', '宠物训练'),
        ('pet_diagnosis', '宠物诊疗'),
        ('pet_funeral', '宠物殡葬'),
        ('pet_cafe', '宠物咖啡'),
        ('pet_playground', '宠物乐园'),
        ('pet_event_planning', '宠物活动策划'),
        ('pet_boarding', '宠物寄养'),
        ('pet_photography', '宠物摄影'),
        ('pet_shipping', '宠物托运'),
        ('pet_rental', '宠物租赁'),
        ('talent_training', '人才培训'),
        ('pet_club', '宠物俱乐部'),
        ('comprehensive', '综合服务'),
    ]
    
    # 基本信息
    name = models.CharField(max_length=100, verbose_name="门店名称")
    brand_name = models.CharField(max_length=100, blank=True, null=True, verbose_name="门店简称/品牌名")
    store_type = models.CharField(max_length=20, choices=STORE_TYPE_CHOICES, verbose_name="门店类型")
    main_business = models.TextField(blank=True, null=True, verbose_name="主营业务（多个值用分号分隔）")
    competition_relation = models.CharField(max_length=20, choices=COMPETITION_RELATION_CHOICES, verbose_name="竞争关系")
    address = models.TextField(verbose_name="详细地址")
    longitude = models.FloatField(blank=True, null=True, verbose_name="经度")
    latitude = models.FloatField(blank=True, null=True, verbose_name="纬度")
    business_district = models.CharField(max_length=100, blank=True, null=True, verbose_name="所属商圈/区域")
    transportation_convenience = models.TextField(blank=True, null=True, verbose_name="交通便利性")
    visibility = models.CharField(max_length=20, choices=VISIBILITY_CHOICES, blank=True, null=True, verbose_name="可见性与易达性")
    surrounding_community_type = models.TextField(blank=True, null=True, verbose_name="周边社区类型")
    phone = models.CharField(max_length=100, blank=True, null=True, verbose_name="联系电话（多个号码用分号分隔）")
    wechat_qr = models.TextField(blank=True, null=True, verbose_name="微信客服号/群二维码")
    business_hours_weekday = models.CharField(max_length=100, blank=True, null=True, verbose_name="营业时间(工作日)")
    business_hours_weekend = models.CharField(max_length=100, blank=True, null=True, verbose_name="营业时间(周末/节假日)")
    opening_year = models.IntegerField(blank=True, null=True, verbose_name="开业年份")
    store_area = models.FloatField(blank=True, null=True, verbose_name="门店面积(m²)")
    # 旧的photos字段，保留以兼容现有数据
    photos = models.TextField(blank=True, null=True, verbose_name="门店照片(旧)")
    # 新增一个主照片字段
    main_image = models.ImageField(upload_to='store_images/%Y/%m/%d/', blank=True, null=True, verbose_name="门店主照片")
    notes = models.TextField(blank=True, null=True, verbose_name="备注")
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = "竞争对手门店"
        verbose_name_plural = "竞争对手门店"


# 新增门店图片模型，用于支持多图片上传
class StoreImage(models.Model):
    """门店照片"""
    CATEGORY_CHOICES = [
        ('storefront', '门店正面/入口'),
        ('window_display', '橱窗展示'),
        ('surroundings', '周边环境'),
        ('business_info', '营业时间/联系方式'),
        ('retail_overview', '零售区整体布局'),
        ('food_section', '主粮区'),
        ('snack_section', '零食区'),
        ('supplies_section', '用品区'),
        ('grooming_products', '洗护/医疗保健品区'),
        ('featured_products', '重点品牌/特色产品'),
        ('price_tags', '价格标签/促销信息'),
        ('checkout_area', '收银台区域'),
        ('grooming_overview', '洗护区全景'),
        ('grooming_station', '单个洗护工位'),
        ('waiting_area', '等待区/家长休息区'),
        ('service_menu', '价目表/服务项目单'),
        ('grooming_brands', '使用的洗护产品品牌'),
        ('brand_elements', '品牌元素/文化墙'),
        ('special_facilities', '特色服务/设施'),
        ('staff_interaction', '员工互动'),
        ('cleanliness', '卫生清洁状况'),
        ('other', '其他'),
    ]
    
    store = models.ForeignKey(CompetitorStore, on_delete=models.CASCADE, related_name='store_images', verbose_name="门店")
    image = models.ImageField(upload_to='store_images/%Y/%m/%d/', verbose_name="门店照片")
    caption = models.CharField(max_length=200, blank=True, null=True, verbose_name="图片说明")
    category = models.CharField(max_length=50, choices=CATEGORY_CHOICES, default='other', verbose_name="图片类别")
    order = models.PositiveIntegerField(default=0, verbose_name="排序")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")

    def __str__(self):
        return f"{self.store.name} - {self.get_category_display()} - 照片 {self.id}"

    class Meta:
        verbose_name = "门店照片"
        verbose_name_plural = "门店照片"
        ordering = ['category', 'order', 'uploaded_at']


class ServiceOffered(models.Model):
    """服务项目 (Services Offered)"""
    store = models.ForeignKey(CompetitorStore, on_delete=models.CASCADE, related_name='services', verbose_name="门店")
    service_category = models.CharField(max_length=100, verbose_name="服务大类")
    service_name = models.CharField(max_length=100, verbose_name="服务名称")
    service_description = models.TextField(blank=True, null=True, verbose_name="服务内容描述")
    price_range = models.CharField(max_length=100, blank=True, null=True, verbose_name="价格/价格范围")
    member_price = models.CharField(max_length=100, blank=True, null=True, verbose_name="会员价/套餐价")
    product_brand = models.CharField(max_length=100, blank=True, null=True, verbose_name="使用产品品牌")
    has_cat_area = models.BooleanField(default=False, verbose_name="是否有独立猫咪洗护区")
    notes = models.TextField(blank=True, null=True, verbose_name="备注")
    
    # 多选字段，使用文本存储，前端解析
    appointment_methods = models.CharField(max_length=255, blank=True, null=True, verbose_name="预约方式")
    waiting_area_facilities = models.TextField(blank=True, null=True, verbose_name="等待区设施描述")
    
    def __str__(self):
        return f"{self.store.name} - {self.service_name}"

    class Meta:
        verbose_name = "服务项目"
        verbose_name_plural = "服务项目"


class ProductOffered(models.Model):
    """零售产品 (Products Offered)"""
    store = models.ForeignKey(CompetitorStore, on_delete=models.CASCADE, related_name='products', verbose_name="门店")
    
    # 主营产品大类，多选，用文本存储，前端解析
    main_product_categories = models.TextField(blank=True, null=True, verbose_name="主营产品大类")
    
    # 丰富度评分
    product_richness_score = models.IntegerField(blank=True, null=True, verbose_name="产品丰富度评分")
    product_display_notes = models.TextField(blank=True, null=True, verbose_name="产品陈列与布局评价")
    has_special_products = models.BooleanField(default=False, verbose_name="是否有特色/独家产品")
    special_products_description = models.TextField(blank=True, null=True, verbose_name="特色产品描述")
    
    def __str__(self):
        return f"{self.store.name} - 产品"

    class Meta:
        verbose_name = "零售产品"
        verbose_name_plural = "零售产品"


class BrandInfo(models.Model):
    """品牌信息"""
    POSITIONING_CHOICES = [
        ('high_end', '高端'),
        ('mid_range', '中端'),
        ('economy', '大众/经济'),
    ]
    
    product = models.ForeignKey(ProductOffered, on_delete=models.CASCADE, related_name='brands', verbose_name="产品信息")
    category = models.CharField(max_length=50, verbose_name="品类")
    brand_name = models.CharField(max_length=100, verbose_name="品牌名称")
    positioning = models.CharField(max_length=20, choices=POSITIONING_CHOICES, blank=True, null=True, verbose_name="品牌定位")
    notes = models.TextField(blank=True, null=True, verbose_name="备注/特色")
    
    def __str__(self):
        return f"{self.product.store.name} - {self.category} - {self.brand_name}"

    class Meta:
        verbose_name = "品牌信息"
        verbose_name_plural = "品牌信息"


class PricingStrategy(models.Model):
    """价格与会员体系 (Pricing Strategy)"""
    PRICE_LEVEL_CHOICES = [
        ('high_end', '高端'),
        ('mid_high', '中高端'),
        ('mid_range', '中端'),
        ('mid_low', '中低端'),
        ('economy', '经济型'),
    ]
    
    PRICE_TRANSPARENCY_CHOICES = [
        ('transparent', '公开透明'),
        ('partially', '部分公开'),
        ('inquiry', '需咨询'),
    ]
    
    PROMOTION_FREQUENCY_CHOICES = [
        ('frequent', '频繁'),
        ('quite_often', '较多'),
        ('average', '一般'),
        ('rare', '较少'),
        ('never', '几乎没有'),
    ]
    
    store = models.OneToOneField(CompetitorStore, on_delete=models.CASCADE, related_name='pricing', verbose_name="门店")
    price_level = models.CharField(max_length=20, choices=PRICE_LEVEL_CHOICES, blank=True, null=True, verbose_name="整体价格定位")
    price_transparency = models.CharField(max_length=20, choices=PRICE_TRANSPARENCY_CHOICES, blank=True, null=True, verbose_name="价格透明度")
    has_membership = models.BooleanField(default=False, verbose_name="是否有会员制度")
    membership_types = models.TextField(blank=True, null=True, verbose_name="会员卡类型")
    membership_threshold = models.TextField(blank=True, null=True, verbose_name="办理门槛/费用")
    membership_benefits = models.TextField(blank=True, null=True, verbose_name="会员主要权益")
    membership_attractiveness = models.IntegerField(blank=True, null=True, verbose_name="会员体系吸引力评估")
    promotion_types = models.TextField(blank=True, null=True, verbose_name="常规促销活动类型")
    promotion_frequency = models.CharField(max_length=20, choices=PROMOTION_FREQUENCY_CHOICES, blank=True, null=True, verbose_name="促销活动频率")
    
    def __str__(self):
        return f"{self.store.name} - 价格策略"

    class Meta:
        verbose_name = "价格与会员体系"
        verbose_name_plural = "价格与会员体系"


class BenchmarkProduct(models.Model):
    """标杆商品比价"""
    pricing = models.ForeignKey(PricingStrategy, on_delete=models.CASCADE, related_name='benchmark_products', verbose_name="价格策略")
    product_name = models.CharField(max_length=100, verbose_name="商品名称")
    store_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="本店售价")
    competitor_price = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="竞对售价")
    price_difference = models.DecimalField(max_digits=10, decimal_places=2, blank=True, null=True, verbose_name="差价")
    
    def __str__(self):
        return f"{self.pricing.store.name} - {self.product_name} 比价"

    class Meta:
        verbose_name = "标杆商品比价"
        verbose_name_plural = "标杆商品比价"


class StoreEnvironment(models.Model):
    """门店环境与设施 (Store Environment & Facilities)"""
    CLEANLINESS_CHOICES = [
        (5, '非常干净'),
        (4, '干净'),
        (3, '一般'),
        (2, '较差'),
        (1, '差'),
    ]
    
    SMELL_CHOICES = [
        ('fresh', '清新无异味'),
        ('light', '轻微宠物味'),
        ('moderate', '较重宠物味'),
        ('unpleasant', '难闻'),
    ]
    
    LIGHTING_CHOICES = [
        ('very_bright', '非常明亮'),
        ('bright', '明亮'),
        ('average', '一般'),
        ('dim', '偏暗'),
    ]
    
    VENTILATION_CHOICES = [
        ('good', '良好'),
        ('average', '一般'),
        ('poor', '较差'),
    ]
    
    store = models.OneToOneField(CompetitorStore, on_delete=models.CASCADE, related_name='environment', verbose_name="门店")
    cleanliness = models.IntegerField(choices=CLEANLINESS_CHOICES, blank=True, null=True, verbose_name="门店清洁度")
    smell = models.CharField(max_length=20, choices=SMELL_CHOICES, blank=True, null=True, verbose_name="店内气味")
    decoration_style = models.CharField(max_length=100, blank=True, null=True, verbose_name="装修风格")
    layout_rationality = models.IntegerField(blank=True, null=True, verbose_name="空间布局合理性")
    lighting = models.CharField(max_length=20, choices=LIGHTING_CHOICES, blank=True, null=True, verbose_name="采光情况")
    ventilation = models.CharField(max_length=20, choices=VENTILATION_CHOICES, blank=True, null=True, verbose_name="通风情况")
    beauty_equipment = models.TextField(blank=True, null=True, verbose_name="美容区设备情况")
    retail_display = models.TextField(blank=True, null=True, verbose_name="零售区货架与陈列")
    customer_facilities = models.TextField(blank=True, null=True, verbose_name="顾客便利设施")
    
    def __str__(self):
        return f"{self.store.name} - 环境"

    class Meta:
        verbose_name = "门店环境与设施"
        verbose_name_plural = "门店环境与设施"


class MarketingPromotion(models.Model):
    """营销与口碑 (Marketing & Promotion)"""
    ONLINE_ACTIVITY_CHOICES = [
        ('very_active', '非常活跃'),
        ('active', '活跃'),
        ('average', '一般'),
        ('inactive', '不活跃'),
        ('none', '基本无'),
    ]
    
    store = models.OneToOneField(CompetitorStore, on_delete=models.CASCADE, related_name='marketing', verbose_name="门店")
    website_url = models.URLField(blank=True, null=True, verbose_name="官方网站URL")
    wechat_account = models.CharField(max_length=100, blank=True, null=True, verbose_name="微信公众号名称/ID")
    wechat_miniapp = models.CharField(max_length=100, blank=True, null=True, verbose_name="微信小程序名称")
    xiaohongshu_account = models.CharField(max_length=100, blank=True, null=True, verbose_name="小红书账号")
    douyin_account = models.CharField(max_length=100, blank=True, null=True, verbose_name="抖音账号")
    dianping_url = models.URLField(blank=True, null=True, verbose_name="大众点评店铺链接")
    meituan_url = models.URLField(blank=True, null=True, verbose_name="美团店铺链接")
    online_activity = models.CharField(max_length=20, choices=ONLINE_ACTIVITY_CHOICES, blank=True, null=True, verbose_name="线上活跃度评估")
    dianping_score = models.FloatField(blank=True, null=True, verbose_name="大众点评评分")
    dianping_reviews = models.IntegerField(blank=True, null=True, verbose_name="大众点评评论数")
    meituan_score = models.FloatField(blank=True, null=True, verbose_name="美团评分")
    meituan_reviews = models.IntegerField(blank=True, null=True, verbose_name="美团评论数")
    positive_keywords = models.TextField(blank=True, null=True, verbose_name="主要好评关键词")
    negative_keywords = models.TextField(blank=True, null=True, verbose_name="主要差评关键词")
    offline_marketing = models.TextField(blank=True, null=True, verbose_name="线下营销活动记录")
    brand_perception = models.TextField(blank=True, null=True, verbose_name="品牌形象感知")
    
    def __str__(self):
        return f"{self.store.name} - 营销与口碑"

    class Meta:
        verbose_name = "营销与口碑"
        verbose_name_plural = "营销与口碑"


class StaffOperations(models.Model):
    """人员与运营 (Staff & Operations)"""
    SERVICE_ATTITUDE_CHOICES = [
        (5, '非常好'),
        (4, '好'),
        (3, '一般'),
        (2, '较差'),
        (1, '差'),
    ]
    
    APPOINTMENT_FULLNESS_CHOICES = [
        ('very_difficult', '很难约'),
        ('days_ahead', '需提前几天'),
        ('easy', '较容易约'),
        ('anytime', '随时可约'),
    ]
    
    store = models.OneToOneField(CompetitorStore, on_delete=models.CASCADE, related_name='staff', verbose_name="门店")
    total_staff = models.IntegerField(blank=True, null=True, verbose_name="预估员工总数")
    beauticians = models.IntegerField(blank=True, null=True, verbose_name="美容师数量")
    service_attitude = models.IntegerField(choices=SERVICE_ATTITUDE_CHOICES, blank=True, null=True, verbose_name="员工服务态度")
    professional_knowledge = models.IntegerField(blank=True, null=True, verbose_name="员工专业知识水平")
    customer_flow = models.TextField(blank=True, null=True, verbose_name="客流量观察")
    appointment_fullness = models.CharField(max_length=20, choices=APPOINTMENT_FULLNESS_CHOICES, blank=True, null=True, verbose_name="预约饱满度")
    operational_efficiency = models.IntegerField(blank=True, null=True, verbose_name="运营效率感知")
    
    def __str__(self):
        return f"{self.store.name} - 人员与运营"

    class Meta:
        verbose_name = "人员与运营"
        verbose_name_plural = "人员与运营"


class ComprehensiveRating(models.Model):
    """综合评分 (Comprehensive Rating)"""
    store = models.OneToOneField(CompetitorStore, on_delete=models.CASCADE, related_name='rating', verbose_name="门店")
    product_score = models.FloatField(blank=True, null=True, verbose_name="产品力评分")
    service_score = models.FloatField(blank=True, null=True, verbose_name="服务力评分")
    environment_score = models.FloatField(blank=True, null=True, verbose_name="环境评分")
    marketing_score = models.FloatField(blank=True, null=True, verbose_name="营销力评分")
    operations_score = models.FloatField(blank=True, null=True, verbose_name="运营能力评分")
    total_score = models.FloatField(blank=True, null=True, verbose_name="总分")
    rating_level = models.CharField(max_length=50, blank=True, null=True, verbose_name="评级")
    overall_evaluation = models.TextField(blank=True, null=True, verbose_name="整体评价与关键发现")
    
    def __str__(self):
        return f"{self.store.name} - 综合评分"

    class Meta:
        verbose_name = "综合评分"
        verbose_name_plural = "综合评分"


class CompetitorActivity(models.Model):
    """竞品活动追踪 (Competitor Activity Tracking)"""
    ACTIVITY_TYPE_CHOICES = [
        ('promotion', '促销活动'),
        ('event', '营销活动/事件'),
        ('new_product', '新品发布'),
        ('price_change', '价格变动'),
        ('service_change', '服务调整'),
        ('store_change', '门店变动'),
        ('other', '其他'),
    ]
    
    IMPACT_LEVEL_CHOICES = [
        ('high', '高影响'),
        ('medium', '中等影响'),
        ('low', '低影响'),
        ('unknown', '影响未知'),
    ]
    
    STATUS_CHOICES = [
        ('upcoming', '即将开始'),
        ('active', '进行中'),
        ('ended', '已结束'),
        ('recurring', '周期性'),
    ]
    
    store = models.ForeignKey(CompetitorStore, on_delete=models.CASCADE, related_name='activities', verbose_name="门店")
    title = models.CharField(max_length=200, verbose_name="活动标题")
    activity_type = models.CharField(max_length=20, choices=ACTIVITY_TYPE_CHOICES, verbose_name="活动类型")
    description = models.TextField(verbose_name="活动描述")
    start_date = models.DateField(blank=True, null=True, verbose_name="开始日期")
    end_date = models.DateField(blank=True, null=True, verbose_name="结束日期")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, verbose_name="活动状态")
    impact_level = models.CharField(max_length=20, choices=IMPACT_LEVEL_CHOICES, blank=True, null=True, verbose_name="影响级别")
    discount_amount = models.CharField(max_length=100, blank=True, null=True, verbose_name="折扣力度")
    target_customers = models.CharField(max_length=200, blank=True, null=True, verbose_name="目标客户群体")
    participation_threshold = models.TextField(blank=True, null=True, verbose_name="参与门槛")
    marketing_channels = models.TextField(blank=True, null=True, verbose_name="营销渠道")
    response_strategy = models.TextField(blank=True, null=True, verbose_name="应对策略")
    results = models.TextField(blank=True, null=True, verbose_name="活动效果")
    # 旧的photos字段，保留以兼容现有数据
    photos = models.TextField(blank=True, null=True, verbose_name="活动照片/海报(旧)")
    # 新增一个主图片字段
    main_image = models.ImageField(upload_to='activity_images/%Y/%m/%d/', blank=True, null=True, verbose_name="活动主图片")
    notes = models.TextField(blank=True, null=True, verbose_name="备注")
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    
    def __str__(self):
        return f"{self.store.name} - {self.title}"
    
    class Meta:
        verbose_name = "竞品活动追踪"
        verbose_name_plural = "竞品活动追踪"
        ordering = ['-start_date', '-created_at']


# 新增活动图片模型，用于支持多图片上传
class ActivityImage(models.Model):
    """活动照片"""
    activity = models.ForeignKey(CompetitorActivity, on_delete=models.CASCADE, related_name='activity_images', verbose_name="活动")
    image = models.ImageField(upload_to='activity_images/%Y/%m/%d/', verbose_name="活动照片")
    caption = models.CharField(max_length=200, blank=True, null=True, verbose_name="图片说明")
    order = models.PositiveIntegerField(default=0, verbose_name="排序")
    uploaded_at = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")

    def __str__(self):
        return f"{self.activity.title} - 照片 {self.id}"

    class Meta:
        verbose_name = "活动照片"
        verbose_name_plural = "活动照片"
        ordering = ['order', 'uploaded_at']
